import React, { useEffect, useState } from 'react';
import { Avatar, Box, Typography } from '@mui/material';
import { useChatContext, useChannelStateContext } from 'stream-chat-react';

// Accept doctorName and patientName as props
interface CustomChannelHeaderProps {
  doctorName?: string;
  patientName?: string;
  userRole?: 'doctor' | 'patient'; // Add user role to determine perspective
}

const CustomChannelHeader: React.FC<CustomChannelHeaderProps> = ({ doctorName, patientName, userRole = 'patient' }) => {
  const { channel } = useChannelStateContext();
  const { client } = useChatContext();
  const [isOnline, setIsOnline] = useState<boolean>(false);

  useEffect(() => {
    if (!channel || !client) return;

    // Find the doctor member in the channel
    const loadDoctorInfo = async () => {
      try {
        // Get all members of the channel
        const members = Object.values(channel.state.members || {});
        
        // Find the doctor member (user_id starting with 'd_')
        const doctorMember = members.find(member => 
          member.user_id && member.user_id.startsWith('d_')
        );
        
        if (doctorMember && doctorMember.user) {
          // Check online status - this handles the initial state
          if (doctorMember.user.online) {
            setIsOnline(true);
          } else {
            setIsOnline(false);
          }
          
          // Set up a listener for presence changes
          const handleEvent = (event: any) => {
            if (
              event.user && 
              event.user.id === doctorMember.user_id &&
              event.type === 'user.presence.changed'
            ) {
              setIsOnline(event.user.online || false);
            }
          };
          
          // Add the listener
          client.on(event => {
            handleEvent(event);
            return event;
          });
          
          // Optional: you can also explicitly query for the current status
          try {
            const response = await client.queryUsers({ id: doctorMember.user_id });
            if (response.users && response.users.length > 0) {
              setIsOnline(response.users[0].online || false);
            }
          } catch (error) {
            console.error('Error querying user status:', error);
          }
        }
      } catch (error) {
        console.error('Error loading doctor info:', error);
      }
    };
    
    loadDoctorInfo();
    
    // Check user status every 30 seconds as a fallback
    const interval = setInterval(async () => {
      try {
        const members = Object.values(channel.state.members || {});
        const doctorMember = members.find(member => 
          member.user_id && member.user_id.startsWith('d_')
        );
        
        if (doctorMember && doctorMember.user_id) {
          const response = await client.queryUsers({ id: doctorMember.user_id });
          if (response.users && response.users.length > 0) {
            setIsOnline(response.users[0].online || false);
          }
        }
      } catch (error) {
        console.error('Error checking doctor status:', error);
      }
    }, 30000);
    
    return () => clearInterval(interval);
  }, [channel, client]);

  // Show different content based on user role
  if (userRole === 'doctor') {
    // Doctor's perspective - show patient info
    return (
      <Box sx={{
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: 'white'
      }}>
        {/* Patient Info Section */}
        <Box sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center'
        }}>
          <Avatar sx={{ mr: 2, bgcolor: '#4caf50' }}>
            {patientName ? patientName.charAt(0).toUpperCase() : 'P'}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" component="div">
              {patientName || 'Patient'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Patient Conversation
            </Typography>
          </Box>
        </Box>

        {/* Doctor Context Message
        <Box sx={{
          px: 2,
          pb: 2,
          backgroundColor: '#f8f9fa'
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.5 }}>
            You are chatting with {patientName ? patientName.split(' ')[0] : 'this patient'} about their treatment plan request. You can discuss their needs and provide guidance.
          </Typography>
        </Box> */}
      </Box>
    );
  }

  // Patient's perspective - show doctor info (original behavior)
  return (
    <Box sx={{
      borderBottom: '1px solid #e0e0e0',
      backgroundColor: 'white'
    }}>
      {/* Doctor Info Section */}
      <Box sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center'
      }}>
        <Avatar sx={{ mr: 2, bgcolor: '#6366F1' }}>
          HA
        </Avatar>
        <Box>
          <Typography variant="subtitle1" component="div">
            {doctorName ? doctorName : 'Doctor'}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: isOnline ? '#44b700' : '#ccc',
                mr: 1
              }}
            />
            <Typography variant="body2" color="text.secondary">
              {isOnline ? 'Online' : 'Offline'}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Welcome Message Section */}
      <Box sx={{
        px: 2,
        pb: 2,
        backgroundColor: '#f8f9fa'
      }}>
        <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.5 }}>
          Hi {patientName ? patientName.split(' ')[0] : 'Patient'}, we're here to assess your needs. Your doctor will determine if a new treatment plan is right for you and guide you to better health.
        </Typography>
      </Box>
    </Box>
  );
};

export default CustomChannelHeader; 