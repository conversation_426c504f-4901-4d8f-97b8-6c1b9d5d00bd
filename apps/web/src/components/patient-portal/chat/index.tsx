import {
    Chat,
    Channel,
    Window,
    //MessageInput,
    Thread,
    useChannelStateContext,
    useChatContext,
    DefaultStreamChatGenerics,
    Streami18n
} from "stream-chat-react";
import { Channel as StreamChannel, StreamChat } from 'stream-chat';
import "stream-chat-react/dist/css/v2/index.css";
import { Typography, CircularProgress, Box, Alert } from "@mui/material";
import { useState, useEffect, useRef } from "react";
import { useLocation, MakeGenerics } from "@tanstack/react-location";
import { config } from "../../../config";
import chatService, { formatStreamChatId } from '../../../services/chat.service';
import { ApiClient } from "../../../services";
import CustomChannelHeader from './CustomChannelHeader';
import CustomMessageComponent from './CustomMessageComponent';
import CustomMessageInput from '../../doc-portal/chat/CustomMessageInput';
import CustomMessageList from './CustomMessageList';
import '../../doc-portal/chat/layout.css';

// Define type for location search params
type LocationGenerics = MakeGenerics<{
  Search: {
    token: string;
  };
}>;

// API key for Stream Chat
const API_KEY = config.streamChat.apiKey;


// Keep a reference to active clients across hot reloads
const activeClients = new Map<string, StreamChat<DefaultStreamChatGenerics>>();

// Default doctor ID for patient chat
const DEFAULT_DOCTOR_ID = "harvest";

// Add i18n instance with timezone
const i18n = new Streami18n({
  language: 'en',
  timezone: 'Australia/Sydney',
});

// Component to handle marking messages as read
export const MessageReadHandler = () => {
    const { channel } = useChannelStateContext();
    const { client } = useChatContext();
    const userSelectedRef = useRef(false);
    const markReadTimeoutRef = useRef<NodeJS.Timeout>();
    const isMountedRef = useRef(true);

    useEffect(() => {
        // Set mounted flag
        isMountedRef.current = true;

        // Check if we have a valid channel and client
        if (!channel || !client || !client.userID) return;

        // Consider the channel as selected since we auto-select it
        userSelectedRef.current = true;

        const markRead = async () => {
            // Skip if not selected or client is disconnected
            if (!userSelectedRef.current || !client.userID) {
                return;
            }

            try {
                // Check if client is still connected before marking as read
                if (!client.userID) {
                    console.log('Cannot mark as read: client is disconnected');
                    return;
                }

                // Check if channel is still active
                if (!channel.cid) {
                    console.log('Cannot mark as read: channel is no longer active');
                    return;
                }

                await channel.markRead();
            } catch (error) {
                // Check if it's a disconnection error
                if (error instanceof Error &&
                    (error.message.includes('disconnect') ||
                     error.message.includes('after client.disconnect()'))) {
                    console.log('Client disconnected, cannot mark as read');
                } else {
                    console.error('Error marking channel as read:', error);
                }
            }
        };

        // Use a small timeout to ensure client is fully ready
        markReadTimeoutRef.current = setTimeout(() => {
            // Check again if client is still connected
            if (client.userID && isMountedRef.current) {
                markRead();
            }
        }, 100);

        return () => {
            // Mark as unmounted
            isMountedRef.current = false;

            // Clear any pending timeouts
            if (markReadTimeoutRef.current) {
                clearTimeout(markReadTimeoutRef.current);
                markReadTimeoutRef.current = undefined;
            }
        };
    }, [channel, client]);

    return null;
};

const PatientChatWindow: React.FC = () => {
    const [selectedChannel, setSelectedChannel] = useState<StreamChannel<DefaultStreamChatGenerics> | undefined>(undefined);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [client, setClient] = useState<StreamChat<DefaultStreamChatGenerics> | null>(null);
    const [userId, setUserId] = useState<string | null>(null);
    const clientRef = useRef<StreamChat<DefaultStreamChatGenerics> | null>(null);
    const isInitializingRef = useRef(false);
    const shouldCleanupRef = useRef(true);
    const mountedRef = useRef(true);
    const location = useLocation<LocationGenerics>();
    const [doctorName, setDoctorName] = useState<string>('Doctor');
    const [patientName, setPatientName] = useState<string>('Patient');

    // Get the token directly from location - it's already cleaned by our custom parseSearch
    const token = location.current.search.token || '';

    // Helper function to fetch patient data by ID
    async function fetchPatientData(id: string, isPatientId = false) {
        try {
            if (isPatientId) {
                // If we already have a patientID, use that directly
                return await ApiClient.getPatientByIdFromDB(id);
            } else {
                // Otherwise use the zohoId to get the patient data
                return await ApiClient.getPatientByZohoId(id);
            }
        } catch (error) {
            console.error(`Error fetching patient data by ${isPatientId ? 'patientID' : 'zohoId'}:`, error);
            return null;
        }
    }

    // On component mount/unmount
    useEffect(() => {
        mountedRef.current = true;

        return () => {
            mountedRef.current = false;
        };
    }, []);

    // Initialize chat client with patient token
    useEffect(() => {
        // Skip if no token
        if (!token) return;

        // Prevent multiple initializations running at the same time
        if (isInitializingRef.current) {
            return;
        }

        // Start the initialization process
        const initializePatientChat = async () => {
            // Set initializing flag
            isInitializingRef.current = true;
            shouldCleanupRef.current = false;

            try {
                setLoading(true);
                setError(null);

                // Verify API key is available
                if (!API_KEY) {
                    throw new Error("Stream Chat API key is missing. Please contact support.");
                }

                // Get zohoId from token
                const zohoId = token;

                // Step 1: Fetch Zoho contact details to get email
                try {
                    const contactDetails = await ApiClient.getContactDetailsByZohoId(zohoId);

                    // Step 2: If we have an email, try to update the patient record in our database
                    if (contactDetails && contactDetails.success && contactDetails.email) {
                        const updateSuccess = await ApiClient.updatePatientZohoIdByEmail(contactDetails.email, zohoId);

                        // Step 3: If update failed (patient doesn't exist), create the patient from Zoho contact
                        if (!updateSuccess) {

                            try {
                                 await ApiClient.createPatientFromZohoContact(zohoId);
                               
                            } catch (createError) {
                                console.error('Error creating patient from Zoho contact:', createError);
                                // Continue with initialization even if patient creation fails
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error in Zoho contact details lookup:', error);
                    // Continue with initialization even if this step fails
                }

                // Before creating a new client, check if we already have an active one
                // Try to fetch patient data to get the internal patientID
                let patientInternalId = "";
                let formattedChatId = "";
                let patientName = "Patient";

                // Try to fetch patient data by zohoId
                const patientData = await fetchPatientData(zohoId);

                if (patientData) {
                    if (patientData.fullName) {
                        patientName = patientData.fullName;
                    } else if (patientData.firstName) {
                        patientName = patientData.firstName + (patientData.lastName ? ` ${patientData.lastName}` : '');
                    }

                    // Update the patient name state for the header
                    setPatientName(patientName);

                    // Use the patientID from patient data
                    patientInternalId = patientData.patientID || "";
                    formattedChatId = formatStreamChatId(patientInternalId, 'p');

                    // Check if we already have an active client for this patientID
                    const existingClientById = activeClients.get(formattedChatId);
                    if (existingClientById) {
                        clientRef.current = existingClientById;
                        setClient(existingClientById);
                        setUserId(formattedChatId);
                        setLoading(false);
                        return;
                    }
                }

                // If we don't have a patientID or couldn't find a client with it,
                // check if we have a client with zohoId as fallback
                if (!formattedChatId) {
                    formattedChatId = formatStreamChatId(zohoId, 'p');
                    const existingZohoClient = activeClients.get(formattedChatId);
                    if (existingZohoClient) {
                        clientRef.current = existingZohoClient;
                        setClient(existingZohoClient);
                        setUserId(formattedChatId);
                        setLoading(false);
                        return;
                    }
                }

                // Create Stream Chat client if we didn't find an existing one
                let chatClient: StreamChat<DefaultStreamChatGenerics>;

                try {
                    chatClient = new StreamChat<DefaultStreamChatGenerics>(API_KEY);
                    if (!chatClient) {
                        throw new Error("Failed to initialize Stream Chat client");
                    }
                } catch (clientError) {
                    console.error('Error creating Stream Chat client:', clientError);
                    throw new Error(`Failed to initialize chat client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
                }

                // Connect user with the token
                const userData = {
                    id: formattedChatId,
                    name: patientName,
                    patientId: patientInternalId || "", // Internal patient ID
                    zohoId: zohoId, // Keep zohoId for reference
                    role: 'patient'
                };

                // Get a token from backend for this patient
                const tokenResponse = await chatService.generateToken(formattedChatId);

                if (!tokenResponse || !tokenResponse.token) {
                    throw new Error("No token received from server");
                }

                // Verify the client is still valid
                if (!chatClient) {
                    throw new Error("Stream Chat client was unexpectedly lost");
                }

                await chatClient.connectUser(userData, tokenResponse.token);

                // Store the client in our refs and state
                clientRef.current = chatClient;
                // Also store it in our active clients map using the formatted patient internal ID
                activeClients.set(formattedChatId, chatClient);

                // Check if component is still mounted
                if (mountedRef.current) {
                    setClient(chatClient);
                    setUserId(formattedChatId);
                }
            } catch (err) {
                console.error("Error initializing patient chat:", err);

                // Set a more descriptive error message based on the error
                if (mountedRef.current) {
                    setError(err instanceof Error ?
                        `Failed to initialize chat: ${err.message}` :
                        "Failed to initialize chat. Please try again later.");
                }

                // Clean up failed client
                // Use token to recreate the ID format we would have used
                const fallbackChatId = formatStreamChatId(token, 'p');
                if (activeClients.has(fallbackChatId)) {
                    const failedClient = activeClients.get(fallbackChatId);
                    try {
                        if (failedClient) {
                            await failedClient.disconnectUser();
                        }
                    } catch (disconnectError) {
                        console.error("Error disconnecting failed client:", disconnectError);
                    }
                    activeClients.delete(fallbackChatId);
                }

                clientRef.current = null;
            } finally {
                // Reset flags
                isInitializingRef.current = false;
                shouldCleanupRef.current = true;

                if (mountedRef.current) {
                    setLoading(false);
                }
            }
        };

        initializePatientChat();

        // Cleanup function
        return () => {
            // Don't clean up during hot reloads or if we're still initializing
            if (!shouldCleanupRef.current || isInitializingRef.current) {
                return;
            }

            // We'll leave the client active in the activeClients map for reuse
            // but we'll null out the ref
            clientRef.current = null;
        };
    }, [token]);

    // Handle disconnection on window unload
    useEffect(() => {
        const handleUnload = () => {
            if (token && userId) {
                // We already have the userId at this point from client initialization
                // which could be either based on patientID or zohoId
                const existingClient = activeClients.get(userId);

                if (existingClient && existingClient.userID) {
                    // Only disconnect if the client is still connected
                    try {
                        // Fire and forget - we're unloading anyway
                        existingClient.disconnectUser().catch(e => {
                            console.error('Error disconnecting on unload:', e);
                        });
                    } catch (e) {
                        console.error('Exception during disconnect on unload:', e);
                    } finally {
                        // Always remove from active clients map
                        activeClients.delete(userId);
                    }
                } else if (existingClient) {
                    // If client exists but is not connected, just remove it from the map
                    activeClients.delete(userId);
                }
            }
        };

        window.addEventListener('beforeunload', handleUnload);

        return () => {
            window.removeEventListener('beforeunload', handleUnload);
        };
    }, [token, userId]);

    // State for treatment plan submission status
    const [treatmentPlanSubmitted, setTreatmentPlanSubmitted] = useState<boolean>(false);

    // Find patient's channel or create one if none exists
    useEffect(() => {
        // Skip if we don't have the necessary data
        if (!client || !userId || selectedChannel) return;

        // Check if client is connected
        if (!client.userID) {
            //console.log('Cannot find channels: client is disconnected');
            return;
        }

        let isMounted = true;

        const findOrCreatePatientChannel = async () => {
            try {
                if (!isMounted) return;
                setLoading(true);

                // Check again if client is still connected
                if (!client.userID) {
                    //console.log('Client disconnected before querying channels');
                    return;
                }

                // Query channels where the patient is a member
                const filter = { type: 'messaging', members: { $in: [userId] } };
                const sort = { last_message_at: -1 } as const;

                // Wrap in try/catch to handle potential disconnection during query
                const channels = await client.queryChannels(filter, sort, {
                    watch: true,
                    state: true,
                    presence: true
                });

                // Check if component is still mounted before updating state
                if (!isMounted) return;

                // Check if client is still connected
                if (!client.userID) {
                    //console.log('Client disconnected after querying channels');
                    return;
                }

                if (channels && channels.length > 0) {
                    // Select the most recent channel
                    const patientChannel = channels[0];
                    setSelectedChannel(patientChannel);
                    // Store channel ID in localStorage for read receipts
                    if (patientChannel.id) {
                        localStorage.setItem('lastSelectedChannel', patientChannel.id);
                    }
                } else {
                    console.log('No channels found, creating a new channel for patient with token:', token);

                    try {
                        // Use the token (zohoId) to fetch patient data
                        const zohoId = token;

                        // Fetch patient data to get the name and internal ID
                        let patientName = "Patient";
                        let patientInternalId = ""; // Will store the actual patientId

                        try {
                            // Use the fetchPatientData function from the parent scope
                            const patientData = await fetchPatientData(zohoId);
                            console.log('Patient data:', patientData);

                            if (patientData) {
                                if (patientData.fullName) {
                                    patientName = patientData.fullName;
                                } else if (patientData.firstName) {
                                    patientName = patientData.firstName + (patientData.lastName ? ` ${patientData.lastName}` : '');
                                }

                                // Update the patient name state for the header
                                setPatientName(patientName);

                                // Get the internal patient ID for channel creation
                                patientInternalId = patientData.patientID || "";
                            }

                            // Validate that we have a patientInternalId
                            if (!patientInternalId) {
                                throw new Error('Patient ID not found. Cannot create chat channel.');
                            }

                            // Create a channel between default doctor and this patient
                            const channelName = patientName;


                            // Call the backend API to create a channel - only using patientInternalId
                            const response = await chatService.createChannel(
                                DEFAULT_DOCTOR_ID,
                                patientInternalId,
                                channelName,
                                'doctor',
                                'patient'
                            );

                            if (!response || !response.channelId) {
                                throw new Error('Failed to create channel: No channel ID returned');
                            }

                            // Check if client is still connected before creating channel
                            if (!client.userID) {
                                //.log('Client disconnected before creating channel');
                                return;
                            }

                            // Find the channel we just created
                            const newChannel = client.channel('messaging', response.channelId);

                            // Check if client is still connected before watching channel
                            if (!client.userID) {
                                //console.log('Client disconnected before watching channel');
                                return;
                            }

                            try {
                                // Watch the channel to receive updates
                                await newChannel.watch();

                                // Check if client is still connected and component is mounted
                                if (!client.userID || !isMounted) {
                                    //console.log('Client disconnected or component unmounted after watching channel');
                                    return;
                                }

                                // Set as selected channel
                                setSelectedChannel(newChannel);

                                // Store channel ID in localStorage for read receipts
                                if (newChannel.id) {
                                    localStorage.setItem('lastSelectedChannel', newChannel.id);
                                }
                            } catch (watchError) {
                                // Check if it's a disconnection error
                                if (watchError instanceof Error &&
                                    (watchError.message.includes('disconnect') ||
                                     watchError.message.includes('after client.disconnect()'))) {
                                    //console.log('Client disconnected during channel watch');
                                } else {
                                    console.error('Error watching channel:', watchError);
                                    throw watchError; // Re-throw to be caught by outer catch
                                }
                            }

                        } catch (error) {
                            console.error('Error during channel creation:', error);
                            setError(error instanceof Error ? error.message : "Could not create a chat channel. Please try again later.");
                        }

                    } catch (error) {
                        setError("Could not create a chat channel. Please try again later.");
                    }
                }
            } catch (err) {
                if (!isMounted) return;
                setError("Could not load your chat. Please try again later.");
            } finally {
                if (isMounted) {
                    setLoading(false);
                }
            }
        };

        findOrCreatePatientChannel();

        // Fetch doctor name after channel is set and loading is done
        // This ensures chat loads even if doctor name fetch fails
        return () => {
            isMounted = false;
        };
    }, [client, userId, selectedChannel, token]);

    // Fetch doctor name after selectedChannel is set
    useEffect(() => {
        const fetchDoctorName = async () => {
            if (!selectedChannel) return;
            try {
                const members = Object.values(selectedChannel.state.members || {});
                const patientMember = members.find(m => m.user_id && m.user_id.startsWith('p_'));
                const patientId = patientMember?.user_id?.replace('p_', '');
                if (patientId) {
                    const result = await ApiClient.fetchLatestTreatmentPlanWithDoctorByPatientId(patientId);
                    const doctor = result?.doctor;
                    if (doctor && (doctor.name || doctor.username)) {
                        setDoctorName(doctor.name ?? doctor.username ?? 'Doctor');
                    } else {
                        setDoctorName('Doctor');
                    }
                }
            } catch (err) {
                console.error('Error fetching doctor name:', err);
                setDoctorName('Doctor');
            }
        };
        fetchDoctorName();
    }, [selectedChannel]);

    // Listen for treatment plan submission events
    useEffect(() => {
        if (!selectedChannel || !client) return;


        // Shared helper function to handle termination
        const terminateChat = () => {

            // Set state to show termination message directly
            setTreatmentPlanSubmitted(true);

            // Clean up resources without forcing a reload
            try {
                // Remove the channel from local storage
                localStorage.removeItem('lastSelectedChannel');

                // Disconnect the user after a short delay to allow state update
                setTimeout(async () => {
                    try {
                        if (client.userID) {
                            await client.disconnectUser();

                            // Remove from active clients map
                            if (userId) {
                                activeClients.delete(userId);
                            }
                        }
                    } catch (error) {
                        console.error('Error disconnecting client:', error);
                    }
                }, 1000);
            } catch (error) {
                console.error('Error during chat termination cleanup:', error);
            }
        };

        // Handler for message events
        const handleTreatmentPlanSubmitted = (event: any) => {
            // Avoid processing messages sent BEFORE the chat session started
            const messageTime = event.created_at ? new Date(event.created_at).getTime() : Date.now();
            const sessionStartTimeStr = localStorage.getItem('chatSessionStartTime');
            const sessionStartTime = sessionStartTimeStr ? parseInt(sessionStartTimeStr, 10) : 0;

            // Skip old messages (sent before the session started)
            if (messageTime < sessionStartTime) {
                console.log('Skipping message that was sent before the session started');
                return;
            }


            // Specific treatment plan submission message detection
            let isTerminationMessage = false;

            // 1. Check custom_type field (Stream Chat standard)
            if (event.message?.custom_type === 'treatment.plan.submitted') {
                isTerminationMessage = true;
            }

            // 2. Check alternative custom property format
            else if (event.message?.custom?.type === 'treatment.plan.submitted') {
                isTerminationMessage = true;
            }

            // 3. Check for specific text indicators
            else if (event.message?.text) {
                const text = event.message.text.toLowerCase();

                // Look for the exact phrase we send from the doctor's side
                if (text.includes('doctor has submitted your treatment plan')) {
                    isTerminationMessage = true;
                }

                // Check for our prefix marker
                else if (text.startsWith('treatment_plan_submitted')) {
                    isTerminationMessage = true;
                }
            }

            if (isTerminationMessage) {
                terminateChat();
            }
        };

                 // Create a periodic check that runs every 30 seconds
         const checkInterval = setInterval(async () => {

             try {
                 // Extract patient ID from the formatted user ID
                 const members = Object.values(selectedChannel.state.members || {});
                 const patientMember = members.find(m => m.user_id && m.user_id.startsWith('p_'));
                 const patientId = patientMember?.user_id?.replace('p_', '');

                 if (patientId) {
                     // Get latest treatment plan
                     const result = await ApiClient.fetchLatestTreatmentPlanWithDoctorByPatientId(patientId);

                     if (result?.treatmentPlan) {
                         // Get the stored session start time
                         const sessionStartTimeStr = localStorage.getItem('chatSessionStartTime');
                         const sessionStartTime = sessionStartTimeStr ? parseInt(sessionStartTimeStr, 10) : 0;

                         // Get the plan creation time
                         const planCreationTime = new Date(result.treatmentPlan.createdAt).getTime();

                         // Only terminate if the plan was created AFTER this chat session started
                         if (planCreationTime > sessionStartTime) {
                             terminateChat();
                         } else {
                             console.log("Found treatment plan but it was created before this session started");

                         }
                     }
                 }
             } catch (error) {
                 console.error("Error in periodic treatment plan check:", error);
             }
         }, 30000); // Run every 30 seconds

        // Listen for all message events
        selectedChannel.on('message.new', (event: any) => {
            // First check for visible messages with treatment plan text
            handleTreatmentPlanSubmitted(event);

            // Then check for our hidden message with special type
            if (event.message?.custom_type === 'hidden.treatment.plan.submitted') {
                console.log('Hidden treatment plan message detected');
                terminateChat();
            }
        });

        // Also listen for channel updates which might include system messages
        selectedChannel.on('channel.updated', (event: any) => {
            console.log('Channel update event:', event);
        });

                 // Instead of checking for existing treatment plans on load,
         // store the current time to only detect treatment plans created AFTER the chat starts
         const sessionStartTime = new Date().getTime();

         // Store session start time for the periodic checker to use
         localStorage.setItem('chatSessionStartTime', sessionStartTime.toString());

        // Clean up function
        return () => {
            clearInterval(checkInterval);
            selectedChannel.off('message.new', handleTreatmentPlanSubmitted);
        };
    }, [selectedChannel, client, userId]);

    // Show message if token is missing
    if (!token) {
        return (
            <Box sx={{
                p: 4,
                textAlign: 'center',
                height: '100vh',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <Alert severity="error" sx={{ mb: 2 }}>
                    Invalid access. Please use the link provided by your healthcare provider.
                </Alert>
                <Typography variant="body1">
                    If you believe this is an error, please contact support.
                </Typography>
            </Box>
        );
    }

    // Show loading state
    if (loading) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                width: '100%'
            }}>
                <CircularProgress />
            </Box>
        );
    }

    // Show error state
    if (error || !client) {
        return (
            <Box sx={{
                p: 4,
                textAlign: 'center',
                height: '100vh',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <Typography variant="h6" color="error" gutterBottom>
                    {error || "Unable to initialize chat"}
                </Typography>
                <Typography variant="body1">
                    If this issue persists, please contact support.
                </Typography>
            </Box>
        );
    }

    // Show treatment plan submitted state
    if (treatmentPlanSubmitted) {
        return (
            <Box sx={{
                p: 4,
                textAlign: 'center',
                height: '100vh',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                bgcolor: '#f9f9f9'
            }}>
                <Alert severity="info" sx={{ mb: 3, width: '80%', maxWidth: '500px' }}>
                    Great news! Your treatment plan has been finalized by your doctor. You'll receive an email shortly with all the details. This chat session is now complete.
                </Alert>


            </Box>
        );
    }

    // Render chat interface when client is ready and channel is found
    return (
        <Box sx={{
            height: '100vh',
            width: '100%',
            display: 'flex',
            flexDirection: 'column'
        }}>
            <Chat client={client} i18nInstance={i18n}>
                {selectedChannel ? (
                    <Channel
                        channel={selectedChannel}
                        markReadOnMount={false}
                        doMarkReadRequest={(_channel) => undefined}
                        Message={(messageProps) => (
                            <CustomMessageComponent {...messageProps} doctorName={doctorName} />
                        )}
                    >
                        <MessageReadHandler />
                        <Window>
                            <CustomChannelHeader doctorName={doctorName} patientName={patientName} />
                            <Box sx={{ height: 'calc(100vh - 170px)', overflow: 'auto' }}>
                                <CustomMessageList userRole="patient" />
                            </Box>
                            <CustomMessageInput
                                placeholder="Need a renewal? Message your doctor now to stay on track"
                                onMessageSend={async (_text) => {
                                    // Try to get zohoId from token (which is zohoId) or from patientData
                                    const zohoId = token;
                                    if (zohoId) {
                                        try {
                                            await ApiClient.updateLastDoctorMessage(zohoId, '', true);
                                        } catch (err) {
                                            console.error('Failed to clear Zoho last doctor message:', err);
                                        }
                                    }
                                }}
                            />
                        </Window>
                        <Thread
                            Message={(messageProps) => (
                                <CustomMessageComponent {...messageProps} doctorName={doctorName} />
                            )}
                        />
                    </Channel>
                ) : (
                    <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        width: '100%',
                        p: 4
                    }}>
                        <CircularProgress />
                    </Box>
                )}
            </Chat>
        </Box>
    );
};

export default PatientChatWindow