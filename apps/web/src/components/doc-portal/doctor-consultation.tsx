import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, Button, Dialog, DialogContent, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import PatientForm from "./patient-form";
import Daily, { DailyCall, DailyEventObjectParticipant, DailyEventObjectParticipantLeft } from "@daily-co/daily-js";
import { ApiClient } from "../../services";
import { useSearch, MakeGenerics, useNavigate } from "@tanstack/react-location";
import { useAuth } from "../../hooks/auth-provider";
import LoadingScreen from "../../utils/loading-screen";
import { usePatient } from "../../hooks/patient-provider";
import VideoCameraFrontIcon from "@mui/icons-material/VideoCameraFront";

import { UserActions } from "../../utils";
// import ChatWindow from "./chat";
import { PatientData, ConsultationOutCome } from "../../types";
import { useTracker } from "../../hooks/activity-tracker-provider";
import {
	ConsultationTimerState,
	saveTimerState,
	loadTimerState,
	clearTimerState,
	clearAllTimerStates,
	createInitialTimerState,
	updateCountdownState,
	updatePauseState,
	calculateRemainingTime,
	isConsultationExpired,
	getRemainingConsultationTime
} from "./utils/timer-persistence";

// Feature flag for new countdown timers - set to false to revert to old implementation
const ENABLE_COUNTDOWN_TIMERS = true;

type UrlProps = MakeGenerics<{
	Search: {
		token: string;
	};
}>;

const DoctorConsultation: React.FC = () => {
	const { token } = useSearch<UrlProps>();
	const [frame, setFrame] = useState<DailyCall | null>(null);
	const componentRef = useRef<HTMLElement | null>(null);
	const { doctor } = useAuth();
	const frameRef = useRef<DailyCall | null>(null);
	const { trackActivity } = useTracker();

	// Helper function to get doctor's consultation duration from localStorage
	const getDoctorConsultationDuration = (): number => {
		if (doctor?.accessID) {
			const storedDuration = localStorage.getItem(`doctorConsultationDuration_${doctor.accessID}`);
			if (storedDuration) {
				const duration = parseInt(storedDuration, 10);
				return isNaN(duration) ? 6 : duration; // Default to 6 if parsing fails
			}
		}
		return 6; // Default to 6 minutes if no doctor or no stored duration
	};
	const [isLoading, setIsLoading] = useState(true);
	const nextpatientTimeOut = useRef<NodeJS.Timeout | undefined>(undefined);
	const ongoingMeetingTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const earlyWarningTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const rejectNextPatientTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const nextPatientTimer = useRef<NodeJS.Timeout | undefined>(undefined);

	// New refs for timer implementation - won't affect existing functionality
	const finalCountdownTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const bufferCountdownTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const finalCountdownInterval = useRef<NodeJS.Timeout | undefined>(undefined);
	const bufferCountdownInterval = useRef<NodeJS.Timeout | undefined>(undefined);

	// Patient dropout countdown timer refs
	const patientDropoutTimer = useRef<NodeJS.Timeout | undefined>(undefined);
	const patientDropoutInterval = useRef<NodeJS.Timeout | undefined>(undefined);
	const patientDropoutCompleted = useRef<boolean>(false);

	// New states for countdown UI - won't affect existing functionality
	const [showFinalCountdown, setShowFinalCountdown] = useState(false);
	const [finalCountdownSeconds, setFinalCountdownSeconds] = useState(60);
	const [showBufferCountdown, setShowBufferCountdown] = useState(false);
	const [bufferCountdownSeconds, setBufferCountdownSeconds] = useState(60);

	// Patient dropout countdown state
	const [showPatientDropoutCountdown, setShowPatientDropoutCountdown] = useState(false);
	const [patientDropoutSeconds, setPatientDropoutSeconds] = useState(30);

	// Patient hasn't joined countdown state
	const [showPatientNotJoinedCountdown, setShowPatientNotJoinedCountdown] = useState(false);
	const [patientNotJoinedSeconds, setPatientNotJoinedSeconds] = useState(30);
	const patientNotJoinedInterval = useRef<NodeJS.Timeout | undefined>(undefined);

	// Unified pause state management
	const activeTriggers = useRef<Set<"typing" | "focus">>(new Set());
	const resumeTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

	// Timer persistence state
	const timerState = useRef<ConsultationTimerState | null>(null);
	const timerStateInitialized = useRef<boolean>(false);

	// Legacy typing detection state (kept for backward compatibility)
	const [isDoctorTyping, setIsDoctorTyping] = useState(false);
	const typingTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

	const pausedCountdowns = useRef<{
		patientDropout: boolean;
		patientNotJoined: boolean;
		finalCountdown: boolean;
		bufferCountdown: boolean;
	}>({
		patientDropout: false,
		patientNotJoined: false,
		finalCountdown: false,
		bufferCountdown: false,
	});

	// Store pre-pause countdown values to apply 5-second buffer rule
	const prePauseValues = useRef<{
		patientDropout: number;
		patientNotJoined: number;
		finalCountdown: number;
		bufferCountdown: number;
	}>({
		patientDropout: 0,
		patientNotJoined: 0,
		finalCountdown: 0,
		bufferCountdown: 0,
	});

	const TYPING_DELAY_MS = 15000; // 15 seconds

	// Helper function to check if doctor has entered meaningful notes
	const hasNotesEntered = () => {
		const notes = patientTreatmentPlan?.drNotes;
		if (!notes || typeof notes !== "string") {
			return false;
		}
		// Check if notes contain non-whitespace content
		const trimmedNotes = notes.trim();
		return trimmedNotes.length > 0;
	};

	// Helper function to check if doctor is alone on call
	const isDoctorAloneOnCall = () => {
		const participantCount = frameRef?.current?.participantCounts?.();
		return !participantCount || participantCount.present < 2;
	};

	// Helper function to apply 5-second buffer rule for countdowns that were ≤ 1 second when paused
	const getResumeValue = (pausedValue: number) => {
		return pausedValue <= 1 || pausedValue === 0 ? pausedValue + 5 : pausedValue;
	};

	// Unified pause controller functions
	const shouldPause = () => {
		return activeTriggers.current.size > 0 && isDoctorAloneOnCall();
	};

	const shouldResume = () => {
		return activeTriggers.current.size === 0;
	};

	const addTrigger = (trigger: "typing" | "focus") => {
		const wasEmpty = activeTriggers.current.size === 0;
		activeTriggers.current.add(trigger);

		// Activate unified pause if this is the first trigger and doctor is alone
		if (wasEmpty && shouldPause()) {
			if (pauseAllCountdowns()) {
				const logMessage =
					trigger === "focus"
						? "Doctor focused on notes - activating unified pause"
						: "Doctor started typing - activating unified pause";
				trackActivity("Doctor", token || "", UserActions.TYPING_STARTED, logMessage, doctor?.id as string);
			}
		}

		// Clear any existing resume timer since activity is active
		if (resumeTimeoutRef.current) {
			clearTimeout(resumeTimeoutRef.current);
			resumeTimeoutRef.current = undefined;
		}
	};

	const removeTrigger = (trigger: "typing" | "focus") => {
		activeTriggers.current.delete(trigger);

		// Start resume timer only if no other activities are active
		if (shouldResume()) {
			startResumeTimer();
			trackActivity(
				"Doctor",
				token || "",
				UserActions.TYPING_ENDED,
				"Doctor activity ended - starting 15s resume delay",
				doctor?.id as string
			);
		}
	};

	const startResumeTimer = () => {
		resumeTimeoutRef.current = setTimeout(() => {
			if (shouldResume()) {
				resumeAllCountdowns();
				trackActivity(
					"Doctor",
					token || "",
					UserActions.TYPING_ENDED,
					"Unified pause ended - resuming countdowns after 15s",
					doctor?.id as string
				);
			}
		}, TYPING_DELAY_MS);
	};

	// Pause all active countdowns when doctor is alone and typing/focused
	const pauseAllCountdowns = () => {
		if (!isDoctorAloneOnCall()) return false;

		// Store current countdown values before pausing
		prePauseValues.current = {
			patientDropout: patientDropoutSeconds,
			patientNotJoined: patientNotJoinedSeconds,
			finalCountdown: finalCountdownSeconds,
			bufferCountdown: bufferCountdownSeconds,
		};

		pausedCountdowns.current = {
			patientDropout: !!patientDropoutInterval.current,
			patientNotJoined: !!patientNotJoinedInterval.current,
			finalCountdown: !!finalCountdownInterval.current,
			bufferCountdown: !!bufferCountdownInterval.current,
		};

		// Update timer state with pause information
		updatePauseInState({
			isPaused: true,
			pausedAt: Date.now(),
			activeTriggers: Array.from(activeTriggers.current),
			prePauseValues: prePauseValues.current
		});

		// Pause visual countdown intervals
		if (patientDropoutInterval.current) {
			clearInterval(patientDropoutInterval.current);
			patientDropoutInterval.current = undefined;
		}
		if (patientNotJoinedInterval.current) {
			clearInterval(patientNotJoinedInterval.current);
			patientNotJoinedInterval.current = undefined;
		}
		if (finalCountdownInterval.current) {
			clearInterval(finalCountdownInterval.current);
			finalCountdownInterval.current = undefined;
		}
		if (bufferCountdownInterval.current) {
			clearInterval(bufferCountdownInterval.current);
			bufferCountdownInterval.current = undefined;
		}

		// Pause the actual redirection timer
		if (nextPatientTimer.current) {
			clearTimeout(nextPatientTimer.current);
			nextPatientTimer.current = undefined;
		}

		return true;
	};

	// Resume previously paused countdowns with 5-second buffer rule
	const resumeAllCountdowns = () => {
		if (pausedCountdowns.current.patientDropout && showPatientDropoutCountdown) {
			// Apply 5-second buffer rule
			const resumeValue = getResumeValue(prePauseValues.current.patientDropout);
			setPatientDropoutSeconds(resumeValue);

			patientDropoutInterval.current = setInterval(() => {
				setPatientDropoutSeconds((prev) => {
					if (prev <= 1 && !patientDropoutCompleted.current) {
						patientDropoutCompleted.current = true;
						clearInterval(patientDropoutInterval.current);
						patientDropoutInterval.current = undefined;
						setShowPatientDropoutCountdown(false);
						const patientToUse = selectedPatientHandler.current || selectedPatient;
						try {
							inviteNextPatient(patientToUse);
						} catch (error) {
							console.error("Error inviting next patient:", error);
						}
						return 0;
					}
					return prev <= 1 ? 0 : prev - 1;
				});
			}, 1000);
		}

		if (pausedCountdowns.current.patientNotJoined && showPatientNotJoinedCountdown) {
			const notesPresent = hasNotesEntered();
			const remainingSeconds = prePauseValues.current.patientNotJoined;
			const isExtendedTimer = notesPresent && remainingSeconds > 1;

			// Apply 5-second buffer rule for UI display
			const resumeValue = getResumeValue(prePauseValues.current.patientNotJoined);
			setPatientNotJoinedSeconds(resumeValue);

			if (isExtendedTimer) {
				// For extended timer cases, freeze the UI countdown after it reaches a reasonable value
				// Let it count down to 1 and then freeze there
				patientNotJoinedInterval.current = setInterval(() => {
					setPatientNotJoinedSeconds((prev) => {
						if (prev <= 1) {
							// Freeze at 1 second when using extended timer
							return 1;
						}
						return prev - 1;
					});
				}, 1000);
			} else {
				// Normal behavior for non-extended timers
				patientNotJoinedInterval.current = setInterval(() => {
					setPatientNotJoinedSeconds((prev) => {
						if (prev <= 1) {
							clearInterval(patientNotJoinedInterval.current);
							patientNotJoinedInterval.current = undefined;
							setShowPatientNotJoinedCountdown(false);
							return 0;
						}
						return prev - 1;
					});
				}, 1000);
			}
		}

		if (pausedCountdowns.current.finalCountdown && showFinalCountdown) {
			// Apply 5-second buffer rule
			const resumeValue = getResumeValue(prePauseValues.current.finalCountdown);
			setFinalCountdownSeconds(resumeValue);

			finalCountdownInterval.current = setInterval(() => {
				setFinalCountdownSeconds((prev) =>
					prev <= 1 ? (clearInterval(finalCountdownInterval.current), 0) : prev - 1
				);
			}, 1000);
		}

		if (pausedCountdowns.current.bufferCountdown && showBufferCountdown) {
			// Apply 5-second buffer rule
			const resumeValue = getResumeValue(prePauseValues.current.bufferCountdown);
			setBufferCountdownSeconds(resumeValue);

			bufferCountdownInterval.current = setInterval(() => {
				setBufferCountdownSeconds((prev) =>
					prev <= 1
						? (clearInterval(bufferCountdownInterval.current), createDefaultTreatmentPlan(), 0)
						: prev - 1
				);
			}, 1000);
		}

		// Restart the nextPatientTimer if patient not joined countdown was active
		if (pausedCountdowns.current.patientNotJoined && showPatientNotJoinedCountdown) {
			// Check if notes are present to determine timer duration
			const notesPresent = hasNotesEntered();
			let timerDuration;

			if (notesPresent) {
				const remainingSeconds = prePauseValues.current.patientNotJoined;

				if (remainingSeconds <= 1) {
					// Keep original 5-second buffer rule for 1 second or less
					timerDuration = getResumeValue(remainingSeconds) * 1000;
					trackActivity(
						"Doctor",
						token || "",
						UserActions.TYPING_ENDED,
						`Applied 5-second buffer rule: ${remainingSeconds}s → ${getResumeValue(remainingSeconds)}s (notes present but ≤1s)`,
						doctor?.id as string
					);
				} else {
					// For all other cases, extend to make total duration 1 minute
					// Calculate how much time has already passed (30 - remaining)
					const timeAlreadyPassed = 30 - remainingSeconds;
					const totalDesiredDuration = 60; // 1 minute
					const newTimerDuration = totalDesiredDuration - timeAlreadyPassed;

					timerDuration = Math.max(newTimerDuration, 5) * 1000; // Minimum 5 seconds
					trackActivity(
						"Doctor",
						token || "",
						UserActions.TYPING_ENDED,
						`Timer extended due to notes: ${remainingSeconds}s remaining → ${newTimerDuration}s (total 1min, UI frozen)`,
						doctor?.id as string
					);
				}
			} else {
				// Use the updated countdown value with buffer applied (original behavior)
				timerDuration = getResumeValue(prePauseValues.current.patientNotJoined) * 1000;
			}

			nextPatientTimer.current = setTimeout(async () => {
				const participantCount = frameRef?.current?.participantCounts?.();
				if (!participantCount || participantCount.present < 2) {
					clearPatientNotJoinedCountdown();
					await inviteNextPatient(selectedPatient);
				}
			}, timerDuration);
		}

		// Log buffer applications for debugging
		const appliedBuffers = [];
		if (pausedCountdowns.current.patientDropout && prePauseValues.current.patientDropout <= 1) {
			appliedBuffers.push(
				`Patient Dropout: ${prePauseValues.current.patientDropout}s → ${getResumeValue(prePauseValues.current.patientDropout)}s`
			);
		}
		if (pausedCountdowns.current.patientNotJoined && prePauseValues.current.patientNotJoined <= 1) {
			appliedBuffers.push(
				`Patient Not Joined: ${prePauseValues.current.patientNotJoined}s → ${getResumeValue(prePauseValues.current.patientNotJoined)}s`
			);
		}
		if (pausedCountdowns.current.finalCountdown && prePauseValues.current.finalCountdown <= 1) {
			appliedBuffers.push(
				`Final Countdown: ${prePauseValues.current.finalCountdown}s → ${getResumeValue(prePauseValues.current.finalCountdown)}s`
			);
		}
		if (pausedCountdowns.current.bufferCountdown && prePauseValues.current.bufferCountdown <= 1) {
			appliedBuffers.push(
				`Buffer Countdown: ${prePauseValues.current.bufferCountdown}s → ${getResumeValue(prePauseValues.current.bufferCountdown)}s`
			);
		}

		if (appliedBuffers.length > 0) {
			trackActivity(
				"Doctor",
				token || "",
				UserActions.TYPING_ENDED,
				`5-second buffer applied: ${appliedBuffers.join(", ")}`,
				doctor?.id as string
			);
		}

		// Update timer state to indicate resume
		updatePauseInState({
			isPaused: false,
			pausedAt: 0,
			activeTriggers: [],
			prePauseValues: {
				patientDropout: 0,
				patientNotJoined: 0,
				finalCountdown: 0,
				bufferCountdown: 0
			}
		});

		pausedCountdowns.current = {
			patientDropout: false,
			patientNotJoined: false,
			finalCountdown: false,
			bufferCountdown: false,
		};
	};

	// Handle typing start - use unified pause system
	const handleTypingStart = () => {
		// Add typing trigger to unified system
		addTrigger("typing");

		// Keep legacy typing detection for backward compatibility
		if (!isDoctorTyping) {
			setIsDoctorTyping(true);
			trackActivity(
				"Doctor",
				token || "",
				UserActions.TYPING_STARTED,
				`Doctor ${doctor?.username} started typing`,
				doctor?.id as string
			);
		}

		// Reset typing timeout for legacy compatibility
		if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
		typingTimeoutRef.current = setTimeout(() => {
			setIsDoctorTyping(false);
			trackActivity(
				"Doctor",
				token || "",
				UserActions.TYPING_ENDED,
				`Doctor ${doctor?.username} stopped typing`,
				doctor?.id as string
			);
			// Remove typing trigger from unified system
			removeTrigger("typing");
		}, TYPING_DELAY_MS);
	};

	// Handle typing stop - remove typing trigger from unified system
	const handleTypingStop = () => {
		// The unified system will handle the pause/resume logic
		// This function is kept for backward compatibility
	};

	// Timer persistence utility functions
	const initializeTimerState = () => {
		if (!token || !doctor?.accessID || timerStateInitialized.current) {
			return;
		}

		// Try to load existing timer state
		const existingState = loadTimerState(token);

		if (existingState) {
			// Check if consultation has expired
			if (isConsultationExpired(existingState)) {
				console.log('Consultation has expired, clearing timer state');
				clearTimerState(token);
				timerState.current = null;
				return;
			}

			// Restore existing state
			timerState.current = existingState;
			console.log('Restored timer state from localStorage:', existingState);
		} else {
			// Create new timer state
			const consultationDurationMinutes = getDoctorConsultationDuration();
			timerState.current = createInitialTimerState(
				token,
				doctor.accessID,
				consultationDurationMinutes
			);
			saveTimerState(timerState.current);
			console.log('Created new timer state:', timerState.current);
		}

		timerStateInitialized.current = true;
	};

	const updateAndSaveTimerState = (updates: Partial<ConsultationTimerState>) => {
		if (!timerState.current) return;

		timerState.current = {
			...timerState.current,
			...updates,
			lastUpdated: Date.now()
		};

		saveTimerState(timerState.current);
	};

	const updateCountdownInState = (
		countdownType: keyof ConsultationTimerState['countdowns'],
		updates: Partial<ConsultationTimerState['countdowns'][typeof countdownType]>
	) => {
		if (!timerState.current) return;

		timerState.current = updateCountdownState(timerState.current, countdownType, updates);
		saveTimerState(timerState.current);
	};

	const updatePauseInState = (pauseUpdates: Partial<ConsultationTimerState['pauseState']>) => {
		if (!timerState.current) return;

		timerState.current = updatePauseState(timerState.current, pauseUpdates);
		saveTimerState(timerState.current);
	};

	const selectedPatientHandler = useRef<PatientData | undefined>(undefined);
	const [showWarningDialog, setShowWarningDailog] = useState(false);
	const [showNexPatientWarning, setShowNextPatientWarning] = useState(false);
	const [_reloadForm, _setReloadForm] = useState(false);

	const {
		patients,
		selectedPatient,
		updateLockStatus,
		setSelectedPlan,
		setStrength,
		setPatientTreatmentPlan,
		patientTreatmentPlan,
		setSelectedPatient,
		setReloadConsultationForm,
		reloadConsultationForm,
	} = usePatient();

	const navigate = useNavigate();
	selectedPatientHandler.current = selectedPatient;

	useEffect(() => {
		const init = () => {
			const existingFrame = Daily.getCallInstance();

			if (!existingFrame) {
				const newFrame = Daily.createFrame(componentRef?.current as HTMLElement, {
					iframeStyle: {
						position: "relative",
						width: "350px",
						height: "600px",
						border: "0",
					},
					startVideoOff: false,
					showLeaveButton: true,
					showFullscreenButton: true,
					userName: `(Zenith) ${doctor?.username ?? "Doctor"}`,
				});
				newFrame.setTheme({
					colors: {
						accent: "#008000",
						accentText: "#FFFFFF",
					},
				});

				newFrame.updateInputSettings({
					video: {
						processor: {
							type: "background-blur",
							config: {
								strength: 1,
							},
						},
					},
				});

				setFrame(newFrame);
				frameRef.current = newFrame;
				newFrame.join({ url: `https://harvest-australia.daily.co/${token}` });
				newFrame.on("left-meeting", () => handleLeave());
				newFrame.on("participant-joined", (event) => handleRemoteJoinedMeeting(event));
				newFrame.on("participant-left", (event) => handleParticipantLeft(event));
				newFrame.on("joined-meeting", () => handleDrJoinedMeeting());

				return newFrame;
			}
		};
		const frame = init();
		setIsLoading(false);
		return () => {
			if (frame) {
				frame.leave();
			}
			clearTimeout(nextPatientTimer.current);
			clearTimeout(rejectNextPatientTimer.current);

			// Clean up new timers
			if (ENABLE_COUNTDOWN_TIMERS) {
				clearTimeout(finalCountdownTimer.current);
				clearTimeout(bufferCountdownTimer.current);
				clearInterval(finalCountdownInterval.current);
				clearInterval(bufferCountdownInterval.current);
				clearTimeout(patientDropoutTimer.current);
				clearInterval(patientDropoutInterval.current);
				clearInterval(patientNotJoinedInterval.current);
			}
			// Clean up typing timeout
			clearTimeout(typingTimeoutRef.current);
			// Clean up unified pause timeout
			clearTimeout(resumeTimeoutRef.current);
		};
	}, [reloadConsultationForm]);

	// Function to start the 60-second countdown at 5 minutes
	const startFinalCountdown = () => {
		if (!ENABLE_COUNTDOWN_TIMERS) return;

		// Clear other countdowns when final countdown starts (call is ending)
		clearPatientDropoutCountdown();
		clearPatientNotJoinedCountdown();

		// Check if we're restoring from saved state
		let initialSeconds = 60;
		if (timerState.current?.countdowns.finalCountdown.active) {
			// Calculate remaining time from saved state
			initialSeconds = calculateRemainingTime(
				timerState.current.countdowns.finalCountdown.seconds,
				timerState.current.countdowns.finalCountdown.startTime
			);
		} else {
			// Update timer state for new countdown
			updateCountdownInState('finalCountdown', {
				active: true,
				seconds: 60,
				startTime: Date.now()
			});
		}

		setShowFinalCountdown(true);
		setFinalCountdownSeconds(initialSeconds);

		finalCountdownInterval.current = setInterval(() => {
			setFinalCountdownSeconds((prev) => {
				const newValue = prev <= 1 ? 0 : prev - 1;

				// Update timer state with current countdown value
				if (timerState.current && newValue > 0) {
					updateCountdownInState('finalCountdown', {
						seconds: newValue
					});
				}

				if (newValue <= 0) {
					clearInterval(finalCountdownInterval.current);
					// Clear countdown from timer state when finished
					updateCountdownInState('finalCountdown', {
						active: false,
						seconds: 0
					});
				}

				return newValue;
			});
		}, 1000);
	};

	// Function to start buffer countdown after call ends
	const startBufferCountdown = () => {
		if (!ENABLE_COUNTDOWN_TIMERS) return;

		// Clear all other countdowns when buffer period starts (call has ended)
		clearPatientDropoutCountdown();
		clearPatientNotJoinedCountdown();
		setShowFinalCountdown(false); // Final countdown should already be done, but ensure it's cleared

		// Check if we're restoring from saved state
		let initialSeconds = 60;
		if (timerState.current?.countdowns.bufferCountdown.active) {
			// Calculate remaining time from saved state
			initialSeconds = calculateRemainingTime(
				timerState.current.countdowns.bufferCountdown.seconds,
				timerState.current.countdowns.bufferCountdown.startTime
			);
		} else {
			// Update timer state for new countdown
			updateCountdownInState('bufferCountdown', {
				active: true,
				seconds: 60,
				startTime: Date.now()
			});
		}

		setShowBufferCountdown(true);
		setBufferCountdownSeconds(initialSeconds);

		bufferCountdownInterval.current = setInterval(() => {
			setBufferCountdownSeconds((prev) => {
				const newValue = prev <= 1 ? 0 : prev - 1;

				// Update timer state with current countdown value
				if (timerState.current && newValue > 0) {
					updateCountdownInState('bufferCountdown', {
						seconds: newValue
					});
				}

				if (newValue <= 0) {
					clearInterval(bufferCountdownInterval.current);
					// Auto create treatment plan when buffer expires
					createDefaultTreatmentPlan();
					// Clear countdown from timer state when finished
					updateCountdownInState('bufferCountdown', {
						active: false,
						seconds: 0
					});
				}

				return newValue;
			});
		}, 1000);
	};

	// Function to start patient dropout countdown
	const startPatientDropoutCountdown = () => {
		if (!ENABLE_COUNTDOWN_TIMERS) {
			return;
		}

		// Don't start if other countdowns are already active
		if (showPatientNotJoinedCountdown || showFinalCountdown || showBufferCountdown) {
			console.log("Not starting patient dropout countdown - other countdown already active");
			return;
		}

		// Clear any existing patient dropout timer
		clearPatientDropoutCountdown();

		// Also clear patient not joined countdown since patient was present but left
		clearPatientNotJoinedCountdown();

		// Reset completion flag
		patientDropoutCompleted.current = false;

		// Check if we're restoring from saved state
		let initialSeconds = 30;
		if (timerState.current?.countdowns.patientDropout.active) {
			// Calculate remaining time from saved state
			initialSeconds = calculateRemainingTime(
				timerState.current.countdowns.patientDropout.seconds,
				timerState.current.countdowns.patientDropout.startTime
			);
		} else {
			// Update timer state for new countdown
			updateCountdownInState('patientDropout', {
				active: true,
				seconds: 30,
				startTime: Date.now()
			});
		}

		setShowPatientDropoutCountdown(true);
		setPatientDropoutSeconds(initialSeconds);

		// Start the countdown interval
		patientDropoutInterval.current = setInterval(() => {
			setPatientDropoutSeconds((prev) => {
				const newValue = prev <= 1 ? 0 : prev - 1;

				// Update timer state with current countdown value
				if (timerState.current && newValue > 0) {
					updateCountdownInState('patientDropout', {
						seconds: newValue
					});
				}

				if (newValue <= 0 && !patientDropoutCompleted.current) {
					// Mark as completed to prevent duplicate execution
					patientDropoutCompleted.current = true;

					clearInterval(patientDropoutInterval.current);
					patientDropoutInterval.current = undefined;
					setShowPatientDropoutCountdown(false);

					// Clear countdown from timer state when finished
					updateCountdownInState('patientDropout', {
						active: false,
						seconds: 0
					});

					trackActivity(
						"Doctor",
						token || "",
						UserActions.TIMEOUT,
						"Patient dropout countdown expired - auto inviting next patient",
						doctor?.id as string
					);

					// Auto invite next patient when countdown expires
					const patientToUse = selectedPatientHandler.current || selectedPatient;
					try {
						inviteNextPatient(patientToUse);
					} catch (error) {
						console.error("Error inviting next patient:", error);
					}
				}

				return newValue;
			});
		}, 1000);

		// Log the start of patient dropout countdown
		trackActivity(
			"Doctor",
			token || "",
			UserActions.TIMER_WARNING,
			`Patient left call - starting 30-second countdown to admit next patient`,
			doctor?.id as string
		);
	};

	// Function to clear patient dropout countdown
	const clearPatientDropoutCountdown = () => {
		if (patientDropoutTimer.current) {
			clearTimeout(patientDropoutTimer.current);
			patientDropoutTimer.current = undefined;
		}
		if (patientDropoutInterval.current) {
			clearInterval(patientDropoutInterval.current);
			patientDropoutInterval.current = undefined;
		}
		setShowPatientDropoutCountdown(false);
		setPatientDropoutSeconds(30);
		patientDropoutCompleted.current = false; // Reset completion flag
	};

	// Function to clear all consultation timers - can be called when consultation ends or doctor makes decisions
	const clearAllConsultationTimers = () => {
		// Clear critical consultation timers
		if (ongoingMeetingTimer.current) {
			clearTimeout(ongoingMeetingTimer.current);
			ongoingMeetingTimer.current = undefined;
		}

		if (earlyWarningTimer.current) {
			clearTimeout(earlyWarningTimer.current);
			earlyWarningTimer.current = undefined;
		}

		if (finalCountdownTimer.current) {
			clearTimeout(finalCountdownTimer.current);
			finalCountdownTimer.current = undefined;
		}

		// Clear UI-related timers
		if (nextpatientTimeOut.current) {
			clearTimeout(nextpatientTimeOut.current);
			nextpatientTimeOut.current = undefined;
		}

		if (rejectNextPatientTimer.current) {
			clearTimeout(rejectNextPatientTimer.current);
			rejectNextPatientTimer.current = undefined;
		}

		if (nextPatientTimer.current) {
			clearTimeout(nextPatientTimer.current);
			nextPatientTimer.current = undefined;
		}

		// Clear countdown intervals and hide UI
		if (ENABLE_COUNTDOWN_TIMERS) {
			if (finalCountdownInterval.current) {
				clearInterval(finalCountdownInterval.current);
				finalCountdownInterval.current = undefined;
			}

			if (bufferCountdownTimer.current) {
				clearTimeout(bufferCountdownTimer.current);
				bufferCountdownTimer.current = undefined;
			}

			if (bufferCountdownInterval.current) {
				clearInterval(bufferCountdownInterval.current);
				bufferCountdownInterval.current = undefined;
			}

			// Hide countdown UI elements
			setShowFinalCountdown(false);
			setShowBufferCountdown(false);
		}

		// Clear patient-related timers
		clearPatientDropoutCountdown();
		clearPatientNotJoinedCountdown();
	};

	// Debug function to check current timer states - can be called from console
	const debugTimerStates = () => {
		const timerStates = {
			consultation: {
				ongoingMeetingTimer: !!ongoingMeetingTimer.current,
				earlyWarningTimer: !!earlyWarningTimer.current,
				finalCountdownTimer: !!finalCountdownTimer.current,
			},
			ui: {
				showFinalCountdown,
				showBufferCountdown,
				showPatientDropoutCountdown,
				showPatientNotJoinedCountdown,
				showWarningDialog,
			},
			intervals: {
				finalCountdownInterval: !!finalCountdownInterval.current,
				bufferCountdownInterval: !!bufferCountdownInterval.current,
				patientDropoutInterval: !!patientDropoutInterval.current,
				patientNotJoinedInterval: !!patientNotJoinedInterval.current,
			},
			patient: selectedPatient?.patientID || "none",
			timestamp: new Date().toLocaleTimeString(),
		};

		console.log("🔍 CURRENT TIMER STATES:", timerStates);
		return timerStates;
	};

	// Make debug function available globally for console access
	React.useEffect(() => {
		(window as any).debugTimerStates = debugTimerStates;
		return () => {
			delete (window as any).debugTimerStates;
		};
	}, []);

	// Function to start patient not joined countdown
	const startPatientNotJoinedCountdown = () => {
		if (!ENABLE_COUNTDOWN_TIMERS) {
			return;
		}

		// Don't start if other countdowns are already active
		if (showPatientDropoutCountdown || showFinalCountdown || showBufferCountdown) {
			console.log("Not starting patient not joined countdown - other countdown already active");
			return;
		}

		// Clear any existing countdown
		clearPatientNotJoinedCountdown();

		// Check if we're restoring from saved state
		let initialSeconds = 30;
		if (timerState.current?.countdowns.patientNotJoined.active) {
			// Calculate remaining time from saved state
			initialSeconds = calculateRemainingTime(
				timerState.current.countdowns.patientNotJoined.seconds,
				timerState.current.countdowns.patientNotJoined.startTime
			);
		} else {
			// Update timer state for new countdown
			updateCountdownInState('patientNotJoined', {
				active: true,
				seconds: 30,
				startTime: Date.now()
			});
		}

		setShowPatientNotJoinedCountdown(true);
		setPatientNotJoinedSeconds(initialSeconds);

		// Start the countdown interval
		patientNotJoinedInterval.current = setInterval(() => {
			setPatientNotJoinedSeconds((prev) => {
				const newValue = prev <= 1 ? 0 : prev - 1;

				// Update timer state with current countdown value
				if (timerState.current && newValue > 0) {
					updateCountdownInState('patientNotJoined', {
						seconds: newValue
					});
				}

				if (newValue <= 0) {
					clearInterval(patientNotJoinedInterval.current);
					patientNotJoinedInterval.current = undefined;
					setShowPatientNotJoinedCountdown(false);

					// Clear countdown from timer state when finished
					updateCountdownInState('patientNotJoined', {
						active: false,
						seconds: 0
					});
				}

				return newValue;
			});
		}, 1000);

		// Log the start of patient not joined countdown
		trackActivity(
			"Doctor",
			token || "",
			UserActions.TIMER_WARNING,
			`Patient hasn't joined - starting 30-second countdown to next patient`,
			doctor?.id as string
		);
	};

	// Function to clear patient not joined countdown
	const clearPatientNotJoinedCountdown = () => {
		if (patientNotJoinedInterval.current) {
			clearInterval(patientNotJoinedInterval.current);
			patientNotJoinedInterval.current = undefined;
		}
		setShowPatientNotJoinedCountdown(false);
		setPatientNotJoinedSeconds(30);
	};

	// Function to create default treatment plan
	const createDefaultTreatmentPlan = () => {
		if (!ENABLE_COUNTDOWN_TIMERS) return;

		// Use the ref value which persists even when state changes
		const patientToUse = selectedPatientHandler.current || selectedPatient;

		if (patientToUse) {
			// Set default values as per requirements
			setSelectedPlan("THC Patient");
			setStrength("22");

			// Create the default treatment plan
			setPatientTreatmentPlan((prev) => {
				const defaultPlan = {
					...prev,
					outcome: "Approve Unrestricted" as ConsultationOutCome, // Cast to the correct type
					patient: patientToUse,
					date: new Date().toISOString(),
					drName: doctor?.username,
					drAphraNumber: doctor?.aphraNumber,
					22: {
						dosePerDay: "0.1",
						maxDosePerDay: "1.0",
						numberOfRepeat: "3",
						totalQuantity: "28",
						supplyInterval: "28",
					},
				};

				// Clean up any other strengths that might be there
				if (defaultPlan[29]) {
					const { 29: _, ...rest } = defaultPlan;
					return rest;
				}

				return defaultPlan;
			});

			// Set a flag to indicate auto-generated plan
			localStorage.setItem("AUTO_GENERATED_PLAN", "true");

			// Trigger form submission using the existing confirm button logic
			handleFormConfirm();
		} else {
			console.error("No patient available for creating default treatment plan");
		}
	};

	// Function to handle form confirmation (same as in PatientForm)
	const handleFormConfirm = async () => {
		// Use the ref value which persists even when state changes
		const patientToUse = selectedPatientHandler.current || selectedPatient;

		if (patientToUse) {
			// Clear all consultation timers since consultation is ending with confirmation
			clearAllConsultationTimers();

			// Access the treatment plan from the context
			localStorage.setItem("END_CALL", "no");

			// Get the treatment plan from context if available
			const tpPlan = localStorage.getItem("TpPlan");
			if (!tpPlan) {
				// If no plan in localStorage, we won't overwrite it
				trackActivity(
					"Doctor",
					patientToUse.patientID || "",
					UserActions.CONFIRMED,
					`${doctor?.username} auto confirmed treatment plan for`,
					doctor?.id as string
				);
			}

			navigate({ to: "/email-confirmation" });
			handleClose();
		} else {
			console.error("No patient available for form confirmation");
		}
	};
	useEffect(() => {
		// Start 30-second countdown as soon as selectedPatient loads
		if (!selectedPatient) {
			if (nextPatientTimer.current) {
				clearTimeout(nextPatientTimer.current);
				nextPatientTimer.current = undefined;
			}
			clearPatientNotJoinedCountdown();
			return;
		}

		// Clear any existing timer and countdown
		if (nextPatientTimer.current) {
			clearTimeout(nextPatientTimer.current);
			nextPatientTimer.current = undefined;
		}
		clearPatientNotJoinedCountdown();

		// Start the countdown UI
		startPatientNotJoinedCountdown();

		nextPatientTimer.current = setTimeout(
			async () => {
				// Check if patient has joined (participant count)
				const participantCount = frameRef?.current?.participantCounts?.();
				if (!participantCount || participantCount.present < 2) {
					clearPatientNotJoinedCountdown(); // Clear countdown before inviting next patient
					await inviteNextPatient(selectedPatient);
				}
			},
			1 * 30 * 1000
		);

		// Cleanup on unmount or when selectedPatient changes
		return () => {
			if (nextPatientTimer.current) {
				clearTimeout(nextPatientTimer.current);
				nextPatientTimer.current = undefined;
			}
			clearPatientNotJoinedCountdown();
		};
	}, [selectedPatient]);

	const inviteNextPatient = async (selectedPatientHandler?: PatientData) => {
		setShowNextPatientWarning(false);

		// Clear all consultation timers since we're ending current consultation and moving to next patient
		clearAllConsultationTimers();

		// Clear patient dropout countdown when manually inviting next patient
		clearPatientDropoutCountdown();

		// Clear patient not joined countdown when manually inviting next patient
		clearPatientNotJoinedCountdown();

		// Clear timer state for current consultation before moving to next patient
		if (token) {
			clearTimerState(token);
			timerState.current = null;
			timerStateInitialized.current = false;
		}

		const patient = selectedPatientHandler ? selectedPatientHandler : selectedPatient;

		if (patient) {
			const drIdLocal = localStorage.getItem("xdr");
			const doctorID =
				drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : "";

			// Always mark current patient as no-show first
			const result = await ApiClient.updatePatientToNoShowInQueue(patient, doctorID);

			// ✅ Update local patient data to reflect no-show status
			// This prevents stale data issues in the frontend
			if (patient.patientID) {
				// Update the current patient object
				patient.noShow = true;
				patient.attempt = (patient.attempt ?? 0) + 1;

				// Also update selectedPatient if it's the same patient
				if (selectedPatient?.patientID === patient.patientID) {
					setSelectedPatient({
						...selectedPatient,
						noShow: true,
						attempt: (selectedPatient.attempt ?? 0) + 1,
					});
				}
			}

			// Log that current patient was marked as no-show
			trackActivity(
				"Doctor",
				patient.patientID || "",
				UserActions.TIMEOUT,
				`Patient ${patient.patientID} marked as no-show - attempt incremented`,
				doctor?.id as string,
				patient.patientID as string
			);

			if (result && result.patientID) {
				setFrame(null);
				updateLockStatus(patient, false, doctorID);
				updateLockStatus(result, true, doctorID);
				setStrength("22");
				setSelectedPlan("");
				setSelectedPatient(undefined);
				setPatientTreatmentPlan(undefined);

				await frameRef?.current?.destroy();
				// await frameRef?.current?.join({ url: `https://harvest-australia.daily.co/${result.patientID}` })
				await ApiClient.alertAwayPatientOnAdmit(result);
				await ApiClient.postRedirect(result);

				try {
					await ApiClient.postPatientAdmission(result.patientID, doctorID);
					trackActivity(
						"Doctor",
						token || "",
						UserActions.TIMEOUT,
						"Patient timed out",
						doctor?.id as string,
						result.patientID as string
					);
					trackActivity(
						"Doctor",
						result.patientID || "",
						UserActions.REDIRECTED,
						"From Time out",
						doctor?.id as string,
						result.patientID as string
					);
					localStorage.removeItem("reloaded");
					// Clear all timer states when transitioning between patients
					clearAllTimerStates();
					navigate({ to: "/doctor-consultation", search: { token: result.patientID } });
					setReloadConsultationForm(!reloadConsultationForm);
				} catch (error: any) {
					if (error?.response?.data?.message === "PATIENT_EXCEEDED_ADMISSION_LIMIT") {
						trackActivity(
							"Doctor",
							result.patientID || "",
							UserActions.TIMEOUT,
							"Patient blocked - exceeded admission limit",
							doctor?.id as string,
							result.patientID as string
						);
						localStorage.removeItem("reloaded");
						// Clear all timer states when leaving consultation
						clearAllTimerStates();
						navigate({ to: "/online-patients" });
					} else {
						throw error; // Re-throw other errors
					}
				}
			} else {
				// Current patient was already marked as no-show above, now redirect since no next patient available
				trackActivity(
					"Doctor",
					"",
					UserActions.REDIRECTED,
					`${doctor?.username} auto redirect to online patient after waiting for 30 seconds. No available patient`,
					doctor?.id as string
				);
				localStorage.removeItem("reloaded");
				// Clear all timer states when leaving consultation
				clearAllTimerStates();
				navigate({ to: "/online-patients" });
			}
		}
	};

	const handleClose = () => {
		if (frame) {
			frame.destroy();
			setFrame(null);
		}
	};

	const handleLeave = async () => {
		// Clear UI-related timers that are no longer needed when doctor leaves
		if (nextpatientTimeOut.current) {
			clearTimeout(nextpatientTimeOut.current);
		}

		if (rejectNextPatientTimer.current) {
			clearTimeout(rejectNextPatientTimer.current);
		}

		if (nextPatientTimer.current) {
			clearTimeout(nextPatientTimer.current);
		}

		// Only clear patient-specific countdown timers when leaving
		if (ENABLE_COUNTDOWN_TIMERS) {
			clearTimeout(patientDropoutTimer.current);
			clearInterval(patientDropoutInterval.current);
			clearInterval(patientNotJoinedInterval.current);

			// Clear only patient-related countdowns (these should not persist)
			setShowPatientDropoutCountdown(false);
			setShowPatientNotJoinedCountdown(false);
		}

		// DO NOT clear these critical timers and UI states - they must persist when doctor rejoins:
		// - ongoingMeetingTimer: Ensures consultation ends at the configured duration
		// - earlyWarningTimer: Doctor should still see 2-minute warning if they rejoin
		// - finalCountdownTimer: Doctor should still see 1-minute countdown if they rejoin
		// - bufferCountdownTimer & bufferCountdownInterval: Doctor should see buffer countdown if they rejoin
		// - showWarningDialog: Should remain visible if warning was already shown
		// - showFinalCountdown & finalCountdownInterval: Should remain visible if countdown was started
		// - showBufferCountdown: Should remain visible if buffer period was started

		// IMPORTANT: All consultation-related timers and UI states are preserved
		// This ensures the doctor sees appropriate warnings/countdowns when they rejoin

		// Clear references for non-critical timers only
		nextpatientTimeOut.current = undefined;
		await frameRef?.current?.destroy();
	};

	const handleRemoteJoinedMeeting = async (event: DailyEventObjectParticipant) => {
		const drName = event?.participant?.user_name.includes("(Zenith)");
		const participantCount = frameRef?.current?.participantCounts();
		if (!drName && participantCount && participantCount.present >= 2) {
			if (nextPatientTimer.current) {
				clearTimeout(nextPatientTimer.current);
			}
			trackActivity(
				"Doctor",
				token || "",
				UserActions.JOINED,
				`Patient ${event.participant.user_name} joined the call with Doctor`,
				doctor?.id as string
			);
			// Clear patient dropout countdown if patient rejoins
			clearPatientDropoutCountdown();

			// Clear patient not joined countdown when patient joins
			clearPatientNotJoinedCountdown();
		}
	};

	const handleParticipantLeft = async (event: DailyEventObjectParticipantLeft) => {
		const drName = event?.participant?.user_name.includes("(Zenith)");
		const participantCount = frameRef?.current?.participantCounts();

		// Only trigger countdown if a patient (not doctor) left and only doctor remains
		if (!drName && participantCount && participantCount.present < 2) {
			// Start the patient dropout countdown
			startPatientDropoutCountdown();

			// Log the patient leaving
			trackActivity(
				"Doctor",
				token || "",
				UserActions.ENDED,
				`Patient left the call - participant count: ${participantCount.present}`,
				doctor?.id as string,
				token as string
			);
		} else {
			console.log("Not starting countdown:", {
				reason: drName ? "Doctor left (not patient)" : "More than 1 participant still present",
				participantCount: participantCount?.present,
			});
			trackActivity(
				"Doctor",
				token || "",
				UserActions.ENDED,
				`${doctor?.username} left the call - participant count: ${participantCount?.present}`,
				doctor?.id as string
			);
		}
	};

	const handleDrJoinedMeeting = async () => {
		// Set default treatment plan when doctor joins meeting

		const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
		if (onlineQueuePatient.length <= 0) {
			if (patients) {
				const timeoutBeforeSendingNotification: number = selectedPatient?.returningPatient ? 180000 : 360000;
				const timeOut = setTimeout(async () => {
					const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
					if (onlineQueuePatient.length <= 0) {
						const drIdLocal = localStorage.getItem("xdr");
						const doctorID =
							drIdLocal && drIdLocal !== ""
								? drIdLocal
								: doctor && doctor.accessID
									? doctor.accessID
									: "";
						await ApiClient.notifyNextPatient(doctorID);
						nextpatientTimeOut.current = undefined;
					}
				}, timeoutBeforeSendingNotification);
				nextpatientTimeOut.current = timeOut;
			}
		}

		// Initialize timer persistence state
		initializeTimerState();

		// Get consultation duration from timer state or default
		const consultationDurationMinutes = timerState.current?.consultation.durationMinutes || getDoctorConsultationDuration();

		// Give Doctor their configured time to wrap up this meeting
		trackActivity(
			"Doctor",
			token || "",
			UserActions.JOINED,
			`${doctor?.username} auto joined (${consultationDurationMinutes} min consultation)`,
			doctor?.id as string,
			token as string
		);

		// Check if we're restoring from a saved state
		if (timerState.current) {
			const remainingConsultationTime = getRemainingConsultationTime(timerState.current);

			// If consultation has time remaining, set up timers with remaining time
			if (remainingConsultationTime > 0) {
				// Warning at 2 minutes before end (if not already shown and time remaining > 2 minutes)
				const warningTimeMs = Math.max(0, (remainingConsultationTime - 120) * 1000);
				if (warningTimeMs > 0 && !timerState.current.consultation.earlyWarningShown) {
					earlyWarningTimer.current = setTimeout(async () => {
						setShowWarningDailog(true);
						updateAndSaveTimerState({
							consultation: {
								...timerState.current!.consultation,
								earlyWarningShown: true
							}
						});
						trackActivity(
							"Doctor",
							token || "",
							UserActions.ENDED,
							`${doctor?.username} was warned about the meeting ending`,
							doctor?.id as string,
							token as string
						);
					}, warningTimeMs);
				}

				// Final countdown at 1 minute before end (if not already triggered and time remaining > 1 minute)
				if (ENABLE_COUNTDOWN_TIMERS) {
					const finalCountdownTimeMs = Math.max(0, (remainingConsultationTime - 60) * 1000);
					if (finalCountdownTimeMs > 0 && !timerState.current.consultation.finalCountdownTriggered) {
						finalCountdownTimer.current = setTimeout(() => {
							startFinalCountdown();
							updateAndSaveTimerState({
								consultation: {
									...timerState.current!.consultation,
									finalCountdownTriggered: true
								}
							});
							trackActivity(
								"Doctor",
								token || "",
								UserActions.TIMER_WARNING,
								`${doctor?.username} received 60-seconds countdown`,
								doctor?.id as string,
								token as string
							);
						}, finalCountdownTimeMs);
					}
				}

				// End call timer with remaining time
				ongoingMeetingTimer.current = setTimeout(async () => {
					await endCall();
				}, remainingConsultationTime * 1000);
			} else {
				// Consultation time has already expired
				console.log('Consultation time has expired, ending call immediately');
				await endCall();
			}
		}
	};

	useEffect(() => {
		const init = async () => {
			if (token) {
				trackActivity(
					"Doctor",
					token,
					UserActions.REACHED,
					`${doctor?.username} reached the Consultation Form`,
					doctor?.id as string,
					token as string
				);
			}
		};
		init();
	}, []);

	useEffect(() => {
		const handleVisibilityChange = async () => {
			if (document.visibilityState === "visible") {
				trackActivity(
					"Doctor",
					"",
					UserActions.RETURNED,
					`${doctor?.username} returned to the consultation form screen`,
					doctor?.id as string,
					token as string
				);
			} else if (document.visibilityState === "hidden") {
				trackActivity(
					"Doctor",
					"",
					UserActions.AWAY,
					`${doctor?.username} left the consultation form screen`,
					doctor?.id as string,
					token as string
				);
			}
		};

		document.addEventListener("visibilitychange", handleVisibilityChange);

		return () => {
			document.removeEventListener("visibilitychange", handleVisibilityChange);
		};
	}, []);

	// Timer restoration useEffect - runs on component mount to restore timer states
	useEffect(() => {
		const restoreTimerStates = () => {
			if (!token || !doctor?.accessID) return;

			// Initialize timer state (this will load existing state if available)
			initializeTimerState();

			// If we have a restored timer state, restore active countdowns
			if (timerState.current) {
				const state = timerState.current;

				// Restore final countdown if it was active
				if (state.countdowns.finalCountdown.active) {
					const remainingSeconds = calculateRemainingTime(
						state.countdowns.finalCountdown.seconds,
						state.countdowns.finalCountdown.startTime
					);

					if (remainingSeconds > 0) {
						setShowFinalCountdown(true);
						setFinalCountdownSeconds(remainingSeconds);

						finalCountdownInterval.current = setInterval(() => {
							setFinalCountdownSeconds((prev) => {
								const newValue = prev <= 1 ? 0 : prev - 1;

								if (timerState.current && newValue > 0) {
									updateCountdownInState('finalCountdown', { seconds: newValue });
								}

								if (newValue <= 0) {
									clearInterval(finalCountdownInterval.current);
									updateCountdownInState('finalCountdown', { active: false, seconds: 0 });
								}

								return newValue;
							});
						}, 1000);
					}
				}

				// Restore buffer countdown if it was active
				if (state.countdowns.bufferCountdown.active) {
					const remainingSeconds = calculateRemainingTime(
						state.countdowns.bufferCountdown.seconds,
						state.countdowns.bufferCountdown.startTime
					);

					if (remainingSeconds > 0) {
						setShowBufferCountdown(true);
						setBufferCountdownSeconds(remainingSeconds);

						bufferCountdownInterval.current = setInterval(() => {
							setBufferCountdownSeconds((prev) => {
								const newValue = prev <= 1 ? 0 : prev - 1;

								if (timerState.current && newValue > 0) {
									updateCountdownInState('bufferCountdown', { seconds: newValue });
								}

								if (newValue <= 0) {
									clearInterval(bufferCountdownInterval.current);
									createDefaultTreatmentPlan();
									updateCountdownInState('bufferCountdown', { active: false, seconds: 0 });
								}

								return newValue;
							});
						}, 1000);
					}
				}

				// Restore patient dropout countdown if it was active
				if (state.countdowns.patientDropout.active) {
					const remainingSeconds = calculateRemainingTime(
						state.countdowns.patientDropout.seconds,
						state.countdowns.patientDropout.startTime
					);

					if (remainingSeconds > 0) {
						setShowPatientDropoutCountdown(true);
						setPatientDropoutSeconds(remainingSeconds);

						patientDropoutInterval.current = setInterval(() => {
							setPatientDropoutSeconds((prev) => {
								const newValue = prev <= 1 ? 0 : prev - 1;

								if (timerState.current && newValue > 0) {
									updateCountdownInState('patientDropout', { seconds: newValue });
								}

								if (newValue <= 0 && !patientDropoutCompleted.current) {
									patientDropoutCompleted.current = true;
									clearInterval(patientDropoutInterval.current);
									patientDropoutInterval.current = undefined;
									setShowPatientDropoutCountdown(false);
									updateCountdownInState('patientDropout', { active: false, seconds: 0 });

									const patientToUse = selectedPatientHandler.current || selectedPatient;
									try {
										inviteNextPatient(patientToUse);
									} catch (error) {
										console.error("Error inviting next patient:", error);
									}
								}

								return newValue;
							});
						}, 1000);
					}
				}

				// Restore patient not joined countdown if it was active
				if (state.countdowns.patientNotJoined.active) {
					const remainingSeconds = calculateRemainingTime(
						state.countdowns.patientNotJoined.seconds,
						state.countdowns.patientNotJoined.startTime
					);

					if (remainingSeconds > 0) {
						setShowPatientNotJoinedCountdown(true);
						setPatientNotJoinedSeconds(remainingSeconds);

						patientNotJoinedInterval.current = setInterval(() => {
							setPatientNotJoinedSeconds((prev) => {
								const newValue = prev <= 1 ? 0 : prev - 1;

								if (timerState.current && newValue > 0) {
									updateCountdownInState('patientNotJoined', { seconds: newValue });
								}

								if (newValue <= 0) {
									clearInterval(patientNotJoinedInterval.current);
									patientNotJoinedInterval.current = undefined;
									setShowPatientNotJoinedCountdown(false);
									updateCountdownInState('patientNotJoined', { active: false, seconds: 0 });
								}

								return newValue;
							});
						}, 1000);
					}
				}

				// Restore pause state if it was active
				if (state.pauseState.isPaused) {
					// Restore active triggers
					activeTriggers.current = new Set(state.pauseState.activeTriggers as ('typing' | 'focus')[]);

					// Restore pre-pause values
					prePauseValues.current = state.pauseState.prePauseValues;

					// Set paused countdowns based on what was active
					pausedCountdowns.current = {
						patientDropout: state.countdowns.patientDropout.active,
						patientNotJoined: state.countdowns.patientNotJoined.active,
						finalCountdown: state.countdowns.finalCountdown.active,
						bufferCountdown: state.countdowns.bufferCountdown.active
					};
				}

				console.log('Timer states restored from localStorage:', state);
			}
		};

		// Only restore on initial mount, not on every render
		if (token && doctor?.accessID && !timerStateInitialized.current) {
			restoreTimerStates();
		}
	}, [token, doctor?.accessID]);

	// Improved function to properly end the call
	const endCall = async () => {
		try {
			// Clear all consultation timers using the centralized function
			clearAllConsultationTimers();

			if (frameRef?.current) {
				// First leave the call
				await frameRef.current.leave();

				// Small delay to ensure leave completes
				await new Promise((resolve) => setTimeout(resolve, 300));

				// Then destroy the frame
				await frameRef.current.destroy();

				// Clear the reference
				frameRef.current = null;
			}

			trackActivity(
				"Doctor",
				token || "",
				UserActions.ENDED,
				"Auto Ended the call",
				doctor?.id as string,
				token as string
			);

			// Start buffer period if feature is enabled
			if (ENABLE_COUNTDOWN_TIMERS) {
				startBufferCountdown();
				trackActivity(
					"Doctor",
					token || "",
					UserActions.BUFFER_PERIOD,
					"Starting buffer period for the treatment plan completion",
					doctor?.id as string,
					token as string
				);
			}
		} catch (error) {
			console.error("Error ending call:", error);
			// If there's an error, still try to start buffer period
			if (ENABLE_COUNTDOWN_TIMERS) {
				startBufferCountdown();
			}
		}
	};

	return (
		<>
			<Dialog
				open={showWarningDialog}
				fullWidth={true}
				maxWidth={"xs"}
				onClose={() => {
					setShowWarningDailog(false);
				}}
			>
				<DialogContent>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>
							Your meeting will end in the next 2 minutes
						</Typography>
						<VideoCameraFrontIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />
						<Typography sx={{ fontSize: "14px" }} align="center">
							Start warpping up this call or you will be automatically removed from this call.
						</Typography>
						<Grid sx={{ mt: 2 }}>
							<Button
								sx={{ color: "green" }}
								onClick={() => {
									setShowWarningDailog(false);
								}}
							>
								Close
							</Button>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>

			{/* Removed final 60-second countdown dialog - using only the non-modal display below video */}

			<Dialog
				open={showNexPatientWarning}
				fullWidth={true}
				maxWidth={"xs"}
				onClose={() => {
					if (rejectNextPatientTimer.current) {
						clearTimeout(rejectNextPatientTimer.current);
						rejectNextPatientTimer.current = undefined;
					}

					rejectNextPatientTimer.current = setTimeout(
						() => {
							const participantCount = frameRef?.current?.participantCounts();
							if (participantCount && participantCount.present > 0 && participantCount.present < 2) {
								setShowNextPatientWarning(true);
							}
						},
						1 * 60 * 1000
					);
					setShowNextPatientWarning(false);
				}}
			>
				<DialogContent>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>
							Patient did not join
						</Typography>
						<VideoCameraFrontIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />
						<Typography sx={{ fontSize: "14px" }} align="center">
							Patient has not joined the meeting after 1 minute wait. Go to the next patient.
						</Typography>
						<Grid sx={{ mt: 2 }}>
							<Button
								sx={{ color: "green" }}
								onClick={() => inviteNextPatient(selectedPatientHandler.current || selectedPatient)}
							>
								Next Patient
							</Button>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>

			{isLoading && <LoadingScreen />}
			<Grid container direction={"row"} spacing={2}>
				<Grid
					container
					size={{ xs: 12, lg: "auto" }}
					justifyContent="end"
					alignItems="end"
					sx={{ width: "100%" }}
				>
					<Box
						ref={componentRef}
						sx={{
							mt: 10,
							position: "fixed",
							top: 0,
							left: 0,
							zIndex: 0,
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
							width: { xs: "100%", lg: "auto" },
						}}
					></Box>

					{/* Final 60-second countdown - positioned below video */}
					{ENABLE_COUNTDOWN_TIMERS && showFinalCountdown && (
						<Box
							sx={{
								position: "fixed",
								bottom: 120,
								left: 20,
								backgroundColor: "rgba(0, 0, 0, 0.8)",
								padding: "8px 12px",
								borderRadius: "4px",
								border: "2px solid #ff9800", // Changed from #f44336 (red) to #ff9800 (amber/warning)
								zIndex: 1000,
								minWidth: 120,
							}}
						>
							<Typography
								align="center"
								sx={{ color: "#ff9800", fontWeight: "bold", fontSize: "18px", mb: 0.5 }}
							>
								CALL ENDING IN
							</Typography>
							<Typography align="center" sx={{ color: "#ff9800", fontWeight: "bold", fontSize: "24px" }}>
								{Math.floor(finalCountdownSeconds / 60)}:
								{(finalCountdownSeconds % 60).toString().padStart(2, "0")}
							</Typography>
						</Box>
					)}

					{/* Buffer countdown - same position as final countdown */}
					{ENABLE_COUNTDOWN_TIMERS && showBufferCountdown && (
						<Box
							sx={{
								position: "fixed",
								bottom: 120,
								left: 20,
								backgroundColor: "rgba(0, 0, 0, 0.8)",
								padding: "8px 12px",
								borderRadius: "4px",
								border: "2px solid #f44336", // Red border to match urgency
								zIndex: 1000,
								minWidth: 200,
							}}
						>
							<Typography
								align="center"
								sx={{ color: "error.main", fontWeight: "bold", fontSize: "16px", mb: 0.5 }}
							>
								AUTO SUBMITTING TREATMENT PLAN IN
							</Typography>
							<Typography
								align="center"
								sx={{ color: "error.main", fontWeight: "bold", fontSize: "24px" }}
							>
								{Math.floor(bufferCountdownSeconds / 60)}:
								{(bufferCountdownSeconds % 60).toString().padStart(2, "0")}
							</Typography>
						</Box>
					)}

					{/* Patient dropout countdown - same position as other countdown timers */}
					{ENABLE_COUNTDOWN_TIMERS && showPatientDropoutCountdown && (
						<Box
							sx={{
								position: "fixed",
								bottom: 120,
								left: 20, // Same position as other countdown timers
								backgroundColor: "rgba(0, 0, 0, 0.8)",
								padding: "8px 12px",
								borderRadius: "4px",
								border: "2px solid #2196f3",
								zIndex: 1000,
								minWidth: 200,
							}}
						>
							<Typography
								align="center"
								sx={{ color: "#2196f3", fontWeight: "bold", fontSize: "16px", mb: 0.5 }}
							>
								PATIENT LEFT - NEXT PATIENT IN
							</Typography>
							<Typography align="center" sx={{ color: "#2196f3", fontWeight: "bold", fontSize: "24px" }}>
								{Math.floor(patientDropoutSeconds / 60)}:
								{(patientDropoutSeconds % 60).toString().padStart(2, "0")}
							</Typography>
						</Box>
					)}

					{/* Patient not joined countdown - same position as other countdown timers */}
					{ENABLE_COUNTDOWN_TIMERS && showPatientNotJoinedCountdown && (
						<Box
							sx={{
								position: "fixed",
								bottom: 120,
								left: 20, // Same position as other countdown timers
								backgroundColor: "rgba(0, 0, 0, 0.8)",
								padding: "8px 12px",
								borderRadius: "4px",
								border: "2px solid #9c27b0",
								zIndex: 1000,
								minWidth: 200,
							}}
						>
							<Typography
								align="center"
								sx={{ color: "#9c27b0", fontWeight: "bold", fontSize: "16px", mb: 0.5 }}
							>
								WAITING FOR PATIENT - NEXT PATIENT IN
							</Typography>
							<Typography align="center" sx={{ color: "#9c27b0", fontWeight: "bold", fontSize: "24px" }}>
								{Math.floor(patientNotJoinedSeconds / 60)}:
								{(patientNotJoinedSeconds % 60).toString().padStart(2, "0")}
							</Typography>
						</Box>
					)}
				</Grid>
				<Grid size={{ lg: 6 }} ml={"auto"}>
					<PatientForm
						handleClose={handleClose}
						reloadForm={reloadConsultationForm}
						setReloadForm={setReloadConsultationForm}
						frameRef={frameRef}
						clearAllConsultationTimers={clearAllConsultationTimers}
						onTypingStart={handleTypingStart}
						onTypingStop={handleTypingStop}
						onFocusStart={() => addTrigger("focus")}
						onFocusEnd={() => removeTrigger("focus")}
					/>
				</Grid>

				{/* <Grid size={{ lg: 3 }} sx={{ width: '100%', height: '90vh', overflowY: 'auto' }}>
                    <ChatWindow 
                        showTestControls={false}
                        showApproveReject={true}
                        channelListHeight="25vh"
                        messageListHeight="30vh"
                        containerStyles={{ height: '100%', minHeight: '100%' }}
                        reloadForm={reloadForm}
                        setReloadForm={setReloadForm}
                        onPrescribe={() => {
                            console.log('Treatment plan updated with prescription');
                        }}
                        onReject={() => {
                            console.log('Rejected consultation');
                        }}
                    />
                </Grid> */}
			</Grid>
		</>
	);
};

export default DoctorConsultation;
