/**
 * Timer Persistence Utilities for Doctor Consultation
 * 
 * Provides localStorage-based persistence for consultation timers to maintain
 * timer state across page refreshes and browser sessions.
 */

export interface ConsultationTimerState {
  consultationId: string; // token/patientID
  doctorId: string; // doctor.accessID
  
  // Main consultation timers
  consultation: {
    startTime: number; // timestamp when consultation started
    durationMinutes: number; // consultation duration
    earlyWarningShown: boolean;
    finalCountdownTriggered: boolean;
  };
  
  // UI Countdown states
  countdowns: {
    finalCountdown: {
      active: boolean;
      seconds: number;
      startTime: number;
    };
    bufferCountdown: {
      active: boolean;
      seconds: number;
      startTime: number;
    };
    patientDropout: {
      active: boolean;
      seconds: number;
      startTime: number;
    };
    patientNotJoined: {
      active: boolean;
      seconds: number;
      startTime: number;
    };
  };
  
  // Pause state management
  pauseState: {
    isPaused: boolean;
    pausedAt: number;
    activeTriggers: string[]; // ['typing', 'focus']
    prePauseValues: {
      patientDropout: number;
      patientNotJoined: number;
      finalCountdown: number;
      bufferCountdown: number;
    };
  };
  
  // Metadata
  lastUpdated: number;
  version: string; // for future compatibility
}

const TIMER_STORAGE_VERSION = '1.0.0';
const TIMER_KEY_PREFIX = 'consultation_timers_';

/**
 * Generate localStorage key for a consultation
 */
export const getTimerStorageKey = (consultationId: string): string => {
  return `${TIMER_KEY_PREFIX}${consultationId}`;
};

/**
 * Save timer state to localStorage
 */
export const saveTimerState = (state: ConsultationTimerState): void => {
  try {
    const stateWithMetadata = {
      ...state,
      lastUpdated: Date.now(),
      version: TIMER_STORAGE_VERSION
    };
    
    const key = getTimerStorageKey(state.consultationId);
    localStorage.setItem(key, JSON.stringify(stateWithMetadata));
  } catch (error) {
    console.error('Failed to save timer state to localStorage:', error);
  }
};

/**
 * Load timer state from localStorage
 */
export const loadTimerState = (consultationId: string): ConsultationTimerState | null => {
  try {
    const key = getTimerStorageKey(consultationId);
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      return null;
    }
    
    const state = JSON.parse(stored) as ConsultationTimerState;
    
    // Version compatibility check
    if (state.version !== TIMER_STORAGE_VERSION) {
      console.warn('Timer state version mismatch, clearing stored state');
      clearTimerState(consultationId);
      return null;
    }
    
    return state;
  } catch (error) {
    console.error('Failed to load timer state from localStorage:', error);
    return null;
  }
};

/**
 * Clear timer state for a specific consultation
 */
export const clearTimerState = (consultationId: string): void => {
  try {
    const key = getTimerStorageKey(consultationId);
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Failed to clear timer state from localStorage:', error);
  }
};

/**
 * Clear all consultation timer states (for patient transitions)
 */
export const clearAllTimerStates = (): void => {
  try {
    const keysToRemove: string[] = [];
    
    // Find all timer-related keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(TIMER_KEY_PREFIX)) {
        keysToRemove.push(key);
      }
    }
    
    // Remove all found keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.error('Failed to clear all timer states from localStorage:', error);
  }
};

/**
 * Calculate elapsed time since a timestamp
 */
export const calculateElapsedTime = (startTime: number): number => {
  return Math.floor((Date.now() - startTime) / 1000);
};

/**
 * Calculate remaining time for a countdown
 */
export const calculateRemainingTime = (
  originalSeconds: number,
  startTime: number
): number => {
  const elapsed = calculateElapsedTime(startTime);
  return Math.max(0, originalSeconds - elapsed);
};

/**
 * Create initial timer state for a new consultation
 */
export const createInitialTimerState = (
  consultationId: string,
  doctorId: string,
  durationMinutes: number
): ConsultationTimerState => {
  const now = Date.now();
  
  return {
    consultationId,
    doctorId,
    consultation: {
      startTime: now,
      durationMinutes,
      earlyWarningShown: false,
      finalCountdownTriggered: false
    },
    countdowns: {
      finalCountdown: {
        active: false,
        seconds: 60,
        startTime: 0
      },
      bufferCountdown: {
        active: false,
        seconds: 60,
        startTime: 0
      },
      patientDropout: {
        active: false,
        seconds: 30,
        startTime: 0
      },
      patientNotJoined: {
        active: false,
        seconds: 30,
        startTime: 0
      }
    },
    pauseState: {
      isPaused: false,
      pausedAt: 0,
      activeTriggers: [],
      prePauseValues: {
        patientDropout: 0,
        patientNotJoined: 0,
        finalCountdown: 0,
        bufferCountdown: 0
      }
    },
    lastUpdated: now,
    version: TIMER_STORAGE_VERSION
  };
};

/**
 * Update countdown state in timer state
 */
export const updateCountdownState = (
  state: ConsultationTimerState,
  countdownType: keyof ConsultationTimerState['countdowns'],
  updates: Partial<ConsultationTimerState['countdowns'][typeof countdownType]>
): ConsultationTimerState => {
  return {
    ...state,
    countdowns: {
      ...state.countdowns,
      [countdownType]: {
        ...state.countdowns[countdownType],
        ...updates
      }
    },
    lastUpdated: Date.now()
  };
};

/**
 * Update pause state in timer state
 */
export const updatePauseState = (
  state: ConsultationTimerState,
  pauseUpdates: Partial<ConsultationTimerState['pauseState']>
): ConsultationTimerState => {
  return {
    ...state,
    pauseState: {
      ...state.pauseState,
      ...pauseUpdates
    },
    lastUpdated: Date.now()
  };
};

/**
 * Check if a timer state is expired (consultation should have ended)
 */
export const isConsultationExpired = (state: ConsultationTimerState): boolean => {
  const consultationDurationMs = state.consultation.durationMinutes * 60 * 1000;
  const elapsed = Date.now() - state.consultation.startTime;
  return elapsed >= consultationDurationMs;
};

/**
 * Get remaining consultation time in seconds
 */
export const getRemainingConsultationTime = (state: ConsultationTimerState): number => {
  const consultationDurationMs = state.consultation.durationMinutes * 60 * 1000;
  const elapsed = Date.now() - state.consultation.startTime;
  const remaining = consultationDurationMs - elapsed;
  return Math.max(0, Math.floor(remaining / 1000));
};
