import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  CircularProgress
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useSnackbar } from 'notistack';
import { ApiClient } from '../../../services';
import chatService from '../../../services/chat.service';
import { useAuth } from '../../../hooks/auth-provider';
import RejectRequestDialog from './RejectRequestDialog';

interface Request {
  id: string;
  type: 'thc_increase' | 'extend_tp';
  patient_id: string;
  patient_name: string;
  patient_dob?: string;
  email: string;
  questionnaire_data: any[];
  total_score: number;
  max_score: number;
  is_eligible: boolean;
  status: string;
  created_at: string;
  approved_at?: string;
  approved_by?: string;
  review_notes?: string;
}

interface RequestCardProps {
  request: Request;
  onUpdate: () => void;
  onPatientMessage: (patientId: string, patientName: string, channelId?: string, request?: Request) => void;
  onExpand?: () => void;
  onCollapse?: () => void;
  isExpanded?: boolean;
  inChatContext?: boolean;
}

const RequestCard: React.FC<RequestCardProps> = ({
  request,
  onUpdate,
  onPatientMessage,
  onExpand,
  onCollapse,
  isExpanded = false,
  inChatContext = false
}) => {
  const [approving, setApproving] = useState(false);
  const [rejecting, setRejecting] = useState(false);
  const [messaging, setMessaging] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const { doctor } = useAuth();
  const { enqueueSnackbar } = useSnackbar();

  const getRequestTitle = () => {
    return request.type === 'thc_increase' 
      ? 'Request: Increase to 29% THC Plan'
      : 'Request: Extend Treatment Plan';
  };

  // const getRiskScoreColor = () => {
  //   const percentage = (request.total_score / request.max_score) * 100;
  //   if (percentage >= 70) return '#4caf50'; // Green
  //   if (percentage >= 40) return '#ff9800'; // Orange
  //   return '#f44336'; // Red
  // };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDateOfBirth = (dobString?: string) => {
    if (!dobString) return null;
    try {
      // Handle different date formats that might come from the database
      const date = new Date(dobString);
      if (isNaN(date.getTime())) return dobString; // Return original if invalid

      return date.toLocaleDateString('en-AU', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    } catch {
      return dobString; // Return original if parsing fails
    }
  };

  const calculateAge = (dobString?: string) => {
    if (!dobString) return null;
    try {
      const birthDate = new Date(dobString);
      if (isNaN(birthDate.getTime())) return null;

      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Adjust age if birthday hasn't occurred this year yet
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      return age;
    } catch {
      return null;
    }
  };

  const handleApprove = async () => {
    try {
      setApproving(true);
      const response = await ApiClient.approveRequest(request.id, {
        doctorId: doctor?.accessID || '',
        doctorName: doctor?.username || ''
      });

      // Clear chat notification for this patient
      try {
        await chatService.clearChatNotificationForRequest(request.email);
        console.log(`Chat notification cleared for patient ${request.email} after approval`);
      } catch (error) {
        console.error('Failed to clear chat notification:', error);
        // Don't fail the approval if notification clearing fails
      }

      // Show success notification
      enqueueSnackbar(response.message || 'Treatment plan approved successfully', {
        variant: 'success',
        autoHideDuration: 5000
      });

      onUpdate();
    } catch (error: any) {
      console.error('Error approving request:', error);

      // Show error notification
      const errorMessage = error?.response?.data?.message || 'Failed to approve treatment plan';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 6000
      });
    } finally {
      setApproving(false);
    }
  };

  const handleReject = () => {
    setRejectDialogOpen(true);
  };

  const handleRejectConfirm = async (rejectionReason: string) => {
    try {
      setRejecting(true);
      const response = await ApiClient.rejectRequest(request.id, {
        rejectionNotes: rejectionReason,
        doctorId: doctor?.accessID || ''
      });

      // Clear chat notification for this patient
      try {
        await chatService.clearChatNotificationForRequest(request.email);
        console.log(`Chat notification cleared for patient ${request.email} after rejection`);
      } catch (error) {
        console.error('Failed to clear chat notification:', error);
        // Don't fail the rejection if notification clearing fails
      }

      // Show success notification
      enqueueSnackbar(response.message || 'Treatment plan rejected successfully', {
        variant: 'success',
        autoHideDuration: 5000
      });

      onUpdate();
    } catch (error: any) {
      console.error('Error rejecting request:', error);

      // Show error notification
      const errorMessage = error?.response?.data?.message || 'Failed to reject treatment plan';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 6000
      });
      throw error; // Re-throw to let dialog handle the error
    } finally {
      setRejecting(false);
    }
  };

  const handleMessagePatient = async () => {
    try {
      setMessaging(true);

      // Get doctor ID, fallback to 'harvest' if not available
      const doctorId = doctor?.accessID || 'harvest';

      // Create or find channel with patient
      const channelResponse = await chatService.createChannel(
        doctorId,
        request.patient_id, // This should be the internal patientID
        `Chat with ${request.patient_name}`,
        'doctor',
        'patient'
      );

      // Call the callback to show chat in the same space
      if (channelResponse?.channelId) {
        onPatientMessage(request.patient_id, request.patient_name, channelResponse.channelId, request);
        console.log('Created/found channel:', channelResponse.channelId);
      } else {
        throw new Error('Failed to create channel');
      }

    } catch (error: any) {
      console.error('Error initiating chat with patient:', error);

      // Show error notification
      const errorMessage = error?.message || 'Failed to initiate chat with patient';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 6000
      });
    } finally {
      setMessaging(false);
    }
  };

  const handleToggle = () => {
    if (isExpanded) {
      onCollapse?.();
    } else {
      onExpand?.();
    }
  };

  // Render content for the request card
  const renderRequestContent = () => (
    <Box sx={{ width: '100%' }}>
      {/* Patient Info */}
      <Typography variant="body2" sx={{ mb: 1 }}>
        <strong>Patient:</strong> {request.patient_name || 'Unknown'}
      </Typography>

      {/* Patient Details */}
      {request.patient_dob && (
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>DOB:</strong> {formatDateOfBirth(request.patient_dob)}
          {calculateAge(request.patient_dob) !== null && (
            <span> • <strong>Age:</strong> {calculateAge(request.patient_dob)}</span>
          )}
        </Typography>
      )}

      {/* Risk Score */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Risk Score:</strong>
          <span style={{ color: '#007F00', fontWeight: 'bold', marginLeft: '8px' }}>
            {Math.round((request.total_score / request.max_score) * 100)}%
          </span>
        </Typography>
      </Box>

      {/* Full Questionnaire when expanded - with reduced height for scrolling */}
      {isExpanded && request.questionnaire_data && request.questionnaire_data.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
            Questionnaire Responses:
          </Typography>
          <Box sx={{ maxHeight: '220px', overflow: 'auto', border: '1px solid #eee', borderRadius: 1, p: 1 }}>
            {request.questionnaire_data.map((qa, index) => (
              <Box key={index} sx={{ mb: 2, pb: 1, borderBottom: index < request.questionnaire_data.length - 1 ? '1px solid #f0f0f0' : 'none' }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  Q. {qa.questionText}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary', pl: 1 }}>
                  A. {qa.answerText}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* Collapsed view - show only first 2 questions */}
      {!isExpanded && request.questionnaire_data && request.questionnaire_data.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
            Sample Questions:
          </Typography>
          {request.questionnaire_data.slice(0, 2).map((qa, index) => (
            <Box key={index} sx={{ mb: 1, pl: 1, borderLeft: '2px solid #eee' }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                Q. {qa.questionText}
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                A. {qa.answerText}
              </Typography>
            </Box>
          ))}
          <Typography variant="caption" sx={{ color: 'primary.main', fontStyle: 'italic' }}>
            Click to expand and see all {request.questionnaire_data.length} questions...
          </Typography>
        </Box>
      )}

      {/* Risk Score Display */}
      {isExpanded && (
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
            Risk Score: <span style={{ color: '#007F00' }}>{Math.round((request.total_score / request.max_score) * 100)}%</span>
          </Typography>
        </Box>
      )}

      {/* Separator Line */}
      {isExpanded && (
        <Box sx={{ borderBottom: '1px solid #333', mb: 2 }} />
      )}

      {/* Action Buttons or Status Display */}
      <Box sx={{ mt: 2 }}>
        {request.status === 'approved' || request.status === 'rejected' ? (
          /* Status display for processed requests */
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 2,
            borderRadius: '8px',
            bgcolor: request.status === 'approved' ? '#e8f5e8' : '#ffebee',
            border: `2px solid ${request.status === 'approved' ? '#4caf50' : '#f44336'}`
          }}>
            <Typography variant="body1" sx={{
              fontWeight: 'bold',
              color: request.status === 'approved' ? '#2e7d32' : '#c62828'
            }}>
              {request.status === 'approved' ? '✅ APPROVED' : '❌ REJECTED'}
              {request.approved_at && (
                <Typography variant="caption" sx={{ display: 'block', mt: 0.5, fontWeight: 'normal' }}>
                  {new Date(request.approved_at).toLocaleDateString('en-AU', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </Typography>
              )}
            </Typography>
          </Box>
        ) : (
          /* Action buttons for pending/submitted requests */
          <Box sx={{ display: 'flex', gap: 1, mb: isExpanded ? 1 : 0 }}>
            <Button
              variant="contained"
              sx={{
                bgcolor: '#4caf50',
                '&:hover': { bgcolor: '#45a049' },
                flex: 1,
                py: 1,
                fontWeight: 'bold',
                borderRadius: '8px'
              }}
              onClick={handleApprove}
              disabled={approving || rejecting}
            >
              {approving ? <CircularProgress size={20} color="inherit" /> : 'Update TP'}
            </Button>
            <Button
              variant="contained"
              sx={{
                bgcolor: '#ff5722',
                '&:hover': { bgcolor: '#e64a19' },
                flex: 1,
                py: 1,
                fontWeight: 'bold',
                borderRadius: '8px'
              }}
              onClick={handleReject}
              disabled={approving || rejecting}
            >
              {rejecting ? <CircularProgress size={20} color="inherit" /> : 'Reject TP'}
            </Button>
          </Box>
        )}

        {/* Message Patient button styled as chat input - only for pending/submitted requests and not in chat context */}
        {isExpanded && !inChatContext && (request.status === 'pending' || request.status === 'submitted') && (
          <Box
            onClick={handleMessagePatient}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              py: 1.5,
              px: 2,
              border: '2px solid #ddd',
              borderRadius: '8px',
              bgcolor: '#f9f9f9',
              cursor: messaging || approving || rejecting ? 'not-allowed' : 'pointer',
              opacity: messaging || approving || rejecting ? 0.6 : 1,
              '&:hover': {
                borderColor: messaging || approving || rejecting ? '#ddd' : '#bbb',
                bgcolor: messaging || approving || rejecting ? '#f9f9f9' : '#f5f5f5'
              }
            }}
          >
            <Typography
              variant="body1"
              sx={{
                color: '#888',
                fontStyle: 'normal',
                fontSize: '16px'
              }}
            >
              {messaging ? 'Opening chat...' : 'Type Your Message'}
            </Typography>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              bgcolor: '#666',
              color: 'white'
            }}>
              {messaging ? (
                <CircularProgress size={16} color="inherit" />
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );

  // Render based on context
  if (inChatContext) {
    return (
      <Box
        sx={{
          border: '1px solid #ddd',
          borderRadius: 2,
          p: 1.5,
          bgcolor: '#ffffff',
          cursor: 'pointer',
          '&:hover': {
            bgcolor: '#f5f5f5'
          },
          // Add visual styling based on status for chat context
          ...(request.status === 'approved' && {
            border: '2px solid #4caf50',
            backgroundColor: '#f8fff8'
          }),
          ...(request.status === 'rejected' && {
            border: '2px solid #f44336',
            backgroundColor: '#fff8f8'
          })
        }}
        onClick={handleToggle}
      >
        {/* Compact header for chat context */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: isExpanded ? 1 : 0
        }}>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              <Typography variant="subtitle2" sx={{ color: '#4caf50', fontWeight: 'bold', fontSize: '0.875rem' }}>
                {getRequestTitle()}
              </Typography>

              {/* Status indicator for approved/rejected requests in chat context */}
              {(request.status === 'approved' || request.status === 'rejected') && (
                <Box sx={{
                  px: 0.5,
                  py: 0.25,
                  borderRadius: 0.5,
                  backgroundColor: request.status === 'approved' ? '#e8f5e8' : '#ffebee',
                  border: `1px solid ${request.status === 'approved' ? '#4caf50' : '#f44336'}`
                }}>
                  <Typography variant="caption" sx={{
                    fontWeight: 'bold',
                    color: request.status === 'approved' ? '#2e7d32' : '#c62828',
                    fontSize: '0.65rem'
                  }}>
                    {request.status === 'approved' ? '✅ APPROVED' : '❌ REJECTED'}
                  </Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              {formatDate(request.created_at)} • Risk Score: {Math.round((request.total_score / request.max_score) * 100)}%
            </Typography>
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
              color: '#666'
            }}
          />
        </Box>

        {/* Expandable content */}
        {isExpanded && (
          <Box sx={{ mt: 1, pt: 1, borderTop: '1px solid #eee' }}>
            {renderRequestContent()}
          </Box>
        )}
      </Box>
    );
  }

  // Normal accordion mode
  return (
    <Accordion
      expanded={isExpanded}
      onChange={handleToggle}
      sx={{
        mb: 1,
        boxShadow: 1,
        // Add visual styling based on status
        ...(request.status === 'approved' && {
          border: '2px solid #4caf50',
          backgroundColor: '#f8fff8'
        }),
        ...(request.status === 'rejected' && {
          border: '2px solid #f44336',
          backgroundColor: '#fff8f8'
        })
      }}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="subtitle2" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
              {getRequestTitle()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {formatDate(request.created_at)}
            </Typography>
          </Box>

          {/* Status indicator for approved/rejected requests */}
          {(request.status === 'approved' || request.status === 'rejected') && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              px: 1,
              py: 0.5,
              borderRadius: 1,
              backgroundColor: request.status === 'approved' ? '#e8f5e8' : '#ffebee',
              border: `1px solid ${request.status === 'approved' ? '#4caf50' : '#f44336'}`
            }}>
              <Typography variant="caption" sx={{
                fontWeight: 'bold',
                color: request.status === 'approved' ? '#2e7d32' : '#c62828',
                fontSize: '0.75rem'
              }}>
                {request.status === 'approved' ? '✅ APPROVED' : '❌ REJECTED'}
              </Typography>
            </Box>
          )}
        </Box>
      </AccordionSummary>

      <AccordionDetails>
        {renderRequestContent()}
      </AccordionDetails>

      {/* Rejection Dialog */}
      <RejectRequestDialog
        open={rejectDialogOpen}
        onClose={() => setRejectDialogOpen(false)}
        onConfirm={handleRejectConfirm}
        patientName={request.patient_name}
        requestType={request.type}
        loading={rejecting}
      />
    </Accordion>
  );
};

export default RequestCard;
