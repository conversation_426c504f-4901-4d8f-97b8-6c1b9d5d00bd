import axios from 'axios';
import { config } from '../config';

// Use the shared API URL from config
const API_BASE_URL = config.apiUrl || 'http://localhost:8080/api';

interface ChatTokenResponse {
  token: string;
  expires_at: number;
}

interface ChannelResponse {
  channelId: string;
  formattedPatientId: string;
  formattedDoctorId: string;
  channel: any;
}

interface ChannelMemberResponse {
  channelId: string;
  members: any[];
}

interface CreateChannelParams {
  userId1: string;
  userId2: string;
  channelName?: string;
  userType1: 'doctor' | 'patient';
  userType2: 'doctor' | 'patient';
}

interface ChatNotificationResponse {
  patientEmail: string;
  chatNotification: boolean;
  lastDoctorMessageAt: string | null;
  doctorId: string | null;
}

/**
 * Format user IDs to be compatible with Stream Chat (a-z, 0-9, @, _, -)
 * @param id - Original ID to format
 * @param prefix - Optional prefix (e.g., 'p_' or 'd_')
 * @returns Formatted ID safe for Stream Chat
 */
export const formatStreamChatId = (id: string, prefix?: string): string => {

  // Max length to allow for IDs, accounting for prefix and potential concatenation in channel IDs
  const MAX_ID_LENGTH = 20;

  // Remove quotes (both single and double) if present in the ID
  const cleanedId = id.toString().replace(/['"]/g, '');

  // Sanitize the ID: replace invalid chars with underscore and convert to lowercase
  const sanitized = cleanedId.replace(/[^a-z0-9@_-]/gi, '_').toLowerCase();

  // Truncate to ensure the ID isn't too long
  const truncated = sanitized.substring(0, MAX_ID_LENGTH);

  // Add prefix if provided
  const result = prefix ? `${prefix}_${truncated}` : truncated;

  return result;
};

/**
 * Service to handle Stream Chat API interactions
 */
class ChatService {
  /**
   * Generate a new token for Stream Chat
   * @param userId - The user ID to generate a token for
   * @returns The token response
   */
  async generateToken(userId: string): Promise<ChatTokenResponse> {
    const response = await axios.post<ChatTokenResponse>(
      `${API_BASE_URL}/chat/v1.0/token`,
      { userId },
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Refresh an existing token for Stream Chat
   * @param userId - The user ID to refresh a token for
   * @returns The new token response
   */
  async refreshToken(userId: string): Promise<ChatTokenResponse> {
    const response = await axios.post<ChatTokenResponse>(
      `${API_BASE_URL}/chat/v1.0/refresh-token`,
      { userId },
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Create a new channel between a patient and doctor
   * @param userId1 - The doctor's ID
   * @param userId2 - The patient's internal ID (not zohoId)
   * @param channelName - Optional custom name for the channel
   * @param userType1 - Type of the first user (should be 'doctor')
   * @param userType2 - Type of the second user (should be 'patient')
   * @returns The created channel response
   * @throws Error if patient ID is not provided or invalid
   */
  async createChannel(
    userId1: string,
    userId2: string,
    channelName?: string,
    userType1: 'doctor' | 'patient' = 'doctor',
    userType2: 'doctor' | 'patient' = 'patient'
  ): Promise<ChannelResponse> {
    // Validate that we have a valid patient ID when userType2 is 'patient'
    if (userType2 === 'patient' && (!userId2 || userId2.trim() === '')) {
      throw new Error('Valid patient ID is required for channel creation');
    }

    const params: CreateChannelParams = {
      userId1,
      userId2,
      channelName,
      userType1,
      userType2
    };

    const response = await axios.post<ChannelResponse>(
      `${API_BASE_URL}/chat/v1.0/channel`,
      params,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get all channels for a user
   * @param userId - The user ID to get channels for
   * @returns Array of channels
   */
  async getUserChannels(userId: string): Promise<any[]> {
    const response = await axios.get(
      `${API_BASE_URL}/chat/v1.0/channels/${userId}`,
      { withCredentials: true }
    );
    return response.data.channels;
  }

  /**
   * Add a member to a channel
   * @param channelId - The channel ID to add a member to
   * @param userId - The user ID to add to the channel
   * @returns The updated channel members
   */
  async addChannelMember(
    channelId: string,
    userId: string
  ): Promise<ChannelMemberResponse> {
    const response = await axios.post<ChannelMemberResponse>(
      `${API_BASE_URL}/chat/v1.0/channel/${channelId}/members`,
      { userId },
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get channels for a patient by patientId
   * @param patientId - The patient ID to get channels for
   * @returns Array of channel IDs
   */
  async getPatientChannels(patientId: string): Promise<string[]> {
    try {
      // Format the patient ID for Stream Chat
      const formattedPatientId = formatStreamChatId(patientId, 'p');

      // Get all channels for the patient
      const channels = await this.getUserChannels(formattedPatientId);

      // Return just the channel IDs
      return channels.map((channel: any) => channel.id);
    } catch (error) {
      console.error('Error getting patient channels:', error);
      return [];
    }
  }

  /**
   * Send a message to a channel indicating that a treatment plan has been submitted
   * @param channelId - The channel ID to send the message to
   * @param messageData - The message data to send
   * @returns Response from the API
   */
  async sendTreatmentPlanCompletedMessage(
    channelId: string,
    messageData: {
      text: string;
      type: string;
      patientId: string;
      timestamp: string;
    }
  ): Promise<any> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/chat/v1.0/channel/${channelId}/message`,
        {
          message: {
            text: messageData.text,
            custom_type: messageData.type,
            patient_id: messageData.patientId,
            timestamp: messageData.timestamp
          }
        },
        { withCredentials: true }
      );
      return response.data;
    } catch (error) {
      console.error('Error sending treatment plan completed message:', error);
      throw error;
    }
  }

  /**
   * Remove a member from a channel
   * @param channelId - The channel ID to remove a member from
   * @param userId - The user ID to remove from the channel
   * @returns The updated channel members
   */
  async removeChannelMember(
    channelId: string,
    userId: string
  ): Promise<ChannelMemberResponse> {
    const response = await axios.delete<ChannelMemberResponse>(
      `${API_BASE_URL}/chat/v1.0/channel/${channelId}/members/${userId}`,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Fetch latest treatment plans for multiple patients (batch)
   * @param patientIds - Array of patient IDs
   * @returns Map of patientId to latest treatment plan (or null)
   */
  async fetchLatestTreatmentPlansForPatients(patientIds: string[]): Promise<Record<string, any>> {
    const response = await axios.post(
      `${API_BASE_URL}/chat/v1.0/patients/latest-treatment-plans`,
      { patientIds },
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Check conversation visibility for moderation
   * @param channelIds - Array of channel IDs to check
   * @returns Response with visibility data
   */
  async checkConversationVisibility(channelIds: string[]): Promise<{ success: boolean; data: Record<string, boolean> }> {
    const response = await axios.post(
      `${API_BASE_URL}/chat/v1.0/check-conversation-visibility`,
      { channelIds },
      { withCredentials: true }
    );

    return response.data;
  }

  /**
   * Check message visibility for moderation (legacy method)
   * @param messageIds - Array of message IDs to check
   * @returns Map of messageId to visibility status
   */
  async checkMessageVisibility(messageIds: string[]): Promise<Record<string, boolean>> {
    const response = await axios.post(
      `${API_BASE_URL}/chat/v1.0/check-message-visibility`,
      { messageIds },
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get chat notification state for a specific patient
   * @param patientEmail - The patient email to check notification for
   * @returns Chat notification data
   */
  async getChatNotification(patientEmail: string): Promise<ChatNotificationResponse> {
    const response = await axios.get<{ data: ChatNotificationResponse }>(
      `${API_BASE_URL}/chat/v1.0/notifications/${encodeURIComponent(patientEmail)}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get all chat notifications with optional filtering
   * @param activeOnly - If true, only return active notifications
   * @returns Array of chat notifications
   */
  async getAllChatNotifications(activeOnly: boolean = false): Promise<ChatNotificationResponse[]> {
    const params = activeOnly ? { active: 'true' } : {};
    const response = await axios.get<{ data: ChatNotificationResponse[] }>(
      `${API_BASE_URL}/chat/v1.0/notifications`,
      {
        params,
        withCredentials: true
      }
    );
    return response.data.data;
  }

  /**
   * Mark chat notification as read for a specific patient
   * @param patientEmail - The patient email to mark notification as read
   * @returns Updated notification data
   */
  async markChatNotificationRead(patientEmail: string): Promise<ChatNotificationResponse> {
    const response = await axios.post<{ data: ChatNotificationResponse }>(
      `${API_BASE_URL}/chat/v1.0/notifications/${encodeURIComponent(patientEmail)}/mark-read`,
      {},
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Set chat notification when doctor sends message
   * @param patientEmail - The patient email
   * @param doctorId - The doctor ID who sent the message
   */
  async setChatNotificationForDoctorMessage(patientEmail: string, doctorId: string): Promise<void> {
    try {
      await axios.post(
        `${API_BASE_URL}/chat/v1.0/notifications/set-message`,
        { patientEmail, doctorId },
        { withCredentials: true }
      );
      console.log(`Chat notification set for patient ${patientEmail} by doctor ${doctorId}`);
    } catch (error) {
      console.error('Failed to set chat notification:', error);
      // Don't throw error to avoid breaking chat functionality
    }
  }

  /**
   * Clear chat notification when doctor approves/rejects request (internal use)
   * @param patientEmail - The patient email to clear notification for
   */
  async clearChatNotificationForRequest(patientEmail: string): Promise<void> {
    try {
      await this.markChatNotificationRead(patientEmail);
    } catch (error) {
      console.error('Failed to clear chat notification:', error);
      // Don't throw error to avoid breaking request functionality
    }
  }
}

// Create a singleton instance
const chatService = new ChatService();

export default chatService;