import { Patient<PERSON>istory, TimeLeft, QuestionnaireShape, TreatmentPlanHistory, PatientOrder, HealthCheckShape } from "../types";
import { toZonedTime, toDate } from "date-fns-tz"; // Correct import

export const getDateTime = (dateTime: string | undefined) => {
  if (dateTime) {
    const date = new Date(dateTime);

    const time = new Intl.DateTimeFormat("en-AU", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
      timeZone: "Australia/Sydney",
    }).format(date);

    const year = new Intl.DateTimeFormat("en-AU", {
      year: "numeric",
      timeZone: "Australia/Sydney",
    }).format(date);

    const month = new Intl.DateTimeFormat("en-AU", {
      month: "2-digit",
      timeZone: "Australia/Sydney",
    }).format(date);

    const day = new Intl.DateTimeFormat("en-AU", {
      day: "2-digit",
      timeZone: "Australia/Sydney",
    }).format(date);

    const dateOnly = `${year}-${month}-${day}`;

    return {
      time,
      date: dateOnly,
    };
  }

  return undefined;
};

export const getDate = (dateTime: string | undefined) => {
  return dateTime ? dateTime.split("T")[0] : undefined;
};

export const calculateTimeLeft = (
  targetDate: string | undefined
): TimeLeft | undefined => {
  const sydneyTime = new Date().toLocaleString("en-US", {
    timeZone: "Australia/Sydney",
  });

  if (targetDate) {
    const total =
      new Date(targetDate).getTime() - new Date(sydneyTime).getTime();

    const seconds = Math.floor((total / 1000) % 60);
    const minutes = Math.floor((total / 1000 / 60) % 60);
    const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
    const days = Math.floor(total / (1000 * 60 * 60 * 24));

    return {
      days,
      hours,
      minutes,
      seconds,
      total,
    };
  }

  return undefined;
};

export const formatDate = (date: string | undefined) => {
  if (!date) return "";
  const newDate = new Date(date);
  const formattedDate = `${newDate.getFullYear()}-${String(newDate.getMonth() + 1).padStart(2, "0")}-${String(newDate.getDate()).padStart(2, "0")}`;
  return formattedDate;
};

export type Sections = {
  [key: string]: {
    start: number;
    end: number;
    title: string;
  };
};

export const healthCheckSections: Sections = {
  section1: {
    start: 1,
    end: 2,
    title: "Health Survey",
  },
  section2: {
    start: 3,
    end: 12,
    title:
      "The following items are about activities you might do during a typical day. Does your health now limit you in these activities? If so, how much?",
  },
  section3: {
    start: 13,
    end: 16,
    title:
      "During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of your physical health?",
  },
  section4: {
    start: 17,
    end: 22,
    title:
      "During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of any emotional problems (such as feeling depressed or anxious)?",
  },
  section5: {
    start: 23,
    end: 32,
    title:
      "These questions are about how you feel and how things have been with you during the past 4 weeks. For each question, please give the one answer that comes closest to the way you have been feeling. How much of the time during the past 4 weeks...",
  },
  section6: {
    start: 33,
    end: 36,
    title: "How TRUE or FALSE is each of the following statements for you.",
  },
};

export const getConsultationDate = (
  startDate: string | undefined,
  endDate?: string | undefined
): string => {
  if (!startDate) return "";

  const timeZone = "Australia/Sydney";

  const parsedStartDate = toDate(new Date(), { timeZone });
  const nowInSydney = toZonedTime(parsedStartDate, timeZone);
  const targetStartDateInSydney = toDate(startDate);
  const targetEndDateInSydney = endDate
    ? toDate(endDate)
    : new Date(targetStartDateInSydney.getTime() + 6 * 60 * 1000);

  const formatTime = (date: Date): string =>
    `${date.getHours().toString().padStart(2, "0")}h${date.getMinutes().toString().padStart(2, "0")}`;

  const yesterdayInSydney = new Date(nowInSydney);
  yesterdayInSydney.setDate(nowInSydney.getDate() - 1);

  const tomorrowInSydney = new Date(nowInSydney);
  tomorrowInSydney.setDate(nowInSydney.getDate() + 1);

  const diffInMs = nowInSydney.getTime() - targetStartDateInSydney.getTime();
  // const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  // const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (
    targetStartDateInSydney.getFullYear() === nowInSydney.getFullYear() &&
    targetStartDateInSydney.getMonth() === nowInSydney.getMonth() &&
    targetStartDateInSydney.getDate() === nowInSydney.getDate()
  ) {
    return `Today between ${formatTime(targetStartDateInSydney)} and ${formatTime(targetEndDateInSydney)}`;
  }

  if (
    targetStartDateInSydney.getFullYear() === yesterdayInSydney.getFullYear() &&
    targetStartDateInSydney.getMonth() === yesterdayInSydney.getMonth() &&
    targetStartDateInSydney.getDate() === yesterdayInSydney.getDate()
  ) {
    return `Yesterday at ${formatTime(targetStartDateInSydney)}`;
  }

  if (diffInDays >= 1) {
    return `${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
  }

  if (
    targetStartDateInSydney.getFullYear() === tomorrowInSydney.getFullYear() &&
    targetStartDateInSydney.getMonth() === tomorrowInSydney.getMonth() &&
    targetStartDateInSydney.getDate() === tomorrowInSydney.getDate()
  ) {
    return `Tomorrow at ${formatTime(targetStartDateInSydney)}`;
  }

  // Handle future consultations or incorrect differences
  const formattedDate = `${targetStartDateInSydney.getFullYear()}-${(
    targetStartDateInSydney.getMonth() + 1
  )
    .toString()
    .padStart(2, "0")}-${targetStartDateInSydney
    .getDate()
    .toString()
    .padStart(2, "0")}`;

  return `${convertDateToReadableFormat(formattedDate)} at ${formatTime(targetStartDateInSydney)}`;
};

export const getNextFollowUp = (daysInterval: number) => {
  const today = new Date();
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + daysInterval);

  const formattedDate = `${futureDate.getFullYear()}-${String(futureDate.getMonth() + 1).padStart(2, "0")}-${String(futureDate.getDate()).padStart(2, "0")}`;

  return formattedDate;
};

export const convertDateToReadableFormat = (
  date: string | undefined
): string => {
  if (!date) return "";
  const ordinalSuffix = (day: number): string => {
    const j = day % 10,
      k = day % 100;
    if (j === 1 && k !== 11) return "st";
    if (j === 2 && k !== 12) return "nd";
    if (j === 3 && k !== 13) return "rd";
    return "th";
  };

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Parse the input date in YYYY-MM-DD format
  const [year, month, day] = date.split("-").map(Number);
  if (
    !day ||
    !month ||
    !year ||
    month < 1 ||
    month > 12 ||
    day < 1 ||
    day > 31
  ) {
    throw new Error("Invalid date format. Use YYYY-MM-DD.");
  }

  // Get the formatted day with ordinal suffix
  const dayWithSuffix = `${day}${ordinalSuffix(day)}`;

  // Get the month name
  const monthName = monthNames[month - 1];

  // Return the formatted date
  return `${dayWithSuffix} ${monthName} ${year}`;
};

export const convertTimeStampToReadableFormat = (
  dateTime: string | undefined
): string => {
  if (!dateTime) return "";

  const ordinalSuffix = (day: number): string => {
    const j = day % 10,
      k = day % 100;
    if (j === 1 && k !== 11) return "st";
    if (j === 2 && k !== 12) return "nd";
    if (j === 3 && k !== 13) return "rd";
    return "th";
  };

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Replace 'T' with a space for easier splitting
  const normalizedDateTime = dateTime.replace("T", " ");
  const [date, time] = normalizedDateTime.split(" ");

  if (!date) {
    throw new Error(
      "Invalid date-time format. Use YYYY-MM-DDTHH:mm:ss.ssssss."
    );
  }

  // Parse the date in YYYY-MM-DD format
  const [year, month, day] = date.split("-").map(Number);
  if (
    !day ||
    !month ||
    !year ||
    month < 1 ||
    month > 12 ||
    day < 1 ||
    day > 31
  ) {
    throw new Error("Invalid date format. Use YYYY-MM-DD.");
  }

  // Get the formatted day with ordinal suffix
  const dayWithSuffix = `${day}${ordinalSuffix(day)}`;

  // Get the month name
  const monthName = monthNames[month - 1];

  // Format the time
  let formattedTime = "";
  if (time) {
    const [hour, minute] = time.split(":").map(Number);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 || 12; // Convert to 12-hour format
    formattedTime = `${formattedHour}:${minute.toString().padStart(2, "0")} ${period}`;
  }

  // Return the formatted date and time
  return `${dayWithSuffix} ${monthName} ${year}${formattedTime ? ` at ${formattedTime}` : ""}`;
};

export const generateTimeSlots = (): string[] => {
  const times: string[] = [];

  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute++) {
      // Format time as HH:mm
      const formattedTime = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
      times.push(formattedTime);
    }
  }

  return times;
};

export const getDayOfWeek = (dateString: string | undefined): string => {
  if (!dateString) return "";
  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  const date = new Date(dateString); // Convert the string to a Date object
  const dayIndex = date.getDay(); // Get the day index (0 = Sunday, 1 = Monday, etc.)

  return daysOfWeek[dayIndex]; // Map the index to the day name
};

// type History = {
//   treatmentPlan: any[] | null;
//   orders: any[] | null;
//   questionnaire: any[] | null;
//   healthCheck: any[] | null;
// };

export const isHistoryEmpty = (history: PatientHistory): boolean => {
  return Object.values(history).every(
    (value) => value === null || (Array.isArray(value) && value.length === 0)
  );
};

export const formatTimeWithMeridian = (timestamp: string | null) => {
  if (!timestamp) return "";
  const date = toDate(timestamp);

  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  const meridian = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12; // Convert 0 (midnight) or 12 (noon) to 12-hour format.

  return `${hours}:${minutes}:${seconds} ${meridian}`;
};

export const safeScriptLinksPerProvince: { [key: string]: string } = {
  ["western australia"]:
    "https://ndewauprdip1.b2clogin.com/ndewauprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=9e1c9574-e8e4-4b34-ba9f-327d4c2e2fd3&scope=https%3A%2F%2Fndewauprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.scriptcheck.health.wa.gov.au%2Fhome&client-request-id=6eabf6de-7d23-4124-9f87-303730e96fff&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=JkqBfKwu8cwrCs3M6AtKZbcnLj2OOonFPs6jBowWKs0&code_challenge_method=S256&nonce=0c234e01-933e-41ff-bdd1-3c177b4facd8&state=eyJpZCI6Ijk2Njg4ODhiLWE1YjMtNDhjYi04ZDJmLTY4M2MyNjBiMDVjOSIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["wa"]:
    "https://ndewauprdip1.b2clogin.com/ndewauprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=9e1c9574-e8e4-4b34-ba9f-327d4c2e2fd3&scope=https%3A%2F%2Fndewauprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.scriptcheck.health.wa.gov.au%2Fhome&client-request-id=6eabf6de-7d23-4124-9f87-303730e96fff&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=JkqBfKwu8cwrCs3M6AtKZbcnLj2OOonFPs6jBowWKs0&code_challenge_method=S256&nonce=0c234e01-933e-41ff-bdd1-3c177b4facd8&state=eyJpZCI6Ijk2Njg4ODhiLWE1YjMtNDhjYi04ZDJmLTY4M2MyNjBiMDVjOSIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",

  ["victoria"]:
    "https://login.microsoftonline.com/c0e0601f-0fac-449c-9c88-a104c4eb9f28/oauth2/v2.0/authorize?client_id=d3c269b3-a152-4380-be9b-3926aca67593&scope=https%3A%2F%2Fdhhsvicgovau.onmicrosoft.com%2Fc66a8124-a958-4474-948b-58d5da87139f%2Fuser_impersonation%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.safescript.vic.gov.au%2Fhome&client-request-id=c3e5bd9e-e6bb-44a6-9d84-d8bc788fe506&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.37.1&client_info=1&code_challenge=RKvAzFA55cffu7LhM-HfXjAdaTS8zXLsWQyPe5Izphw&code_challenge_method=S256&nonce=b7a801cc-c687-4333-9e25-a19ab01ae699&state=eyJpZCI6IjQ3ZDhhMTk0LTdkY2ItNGYxNC1hNDExLWY5NThjN2UzYTlmOCIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D&sso_reload=true",
  ["vic"]:
    "https://login.microsoftonline.com/c0e0601f-0fac-449c-9c88-a104c4eb9f28/oauth2/v2.0/authorize?client_id=d3c269b3-a152-4380-be9b-3926aca67593&scope=https%3A%2F%2Fdhhsvicgovau.onmicrosoft.com%2Fc66a8124-a958-4474-948b-58d5da87139f%2Fuser_impersonation%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.safescript.vic.gov.au%2Fhome&client-request-id=c3e5bd9e-e6bb-44a6-9d84-d8bc788fe506&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.37.1&client_info=1&code_challenge=RKvAzFA55cffu7LhM-HfXjAdaTS8zXLsWQyPe5Izphw&code_challenge_method=S256&nonce=b7a801cc-c687-4333-9e25-a19ab01ae699&state=eyJpZCI6IjQ3ZDhhMTk0LTdkY2ItNGYxNC1hNDExLWY5NThjN2UzYTlmOCIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D&sso_reload=true",

  ["new south wales"]:
    "https://www.safescript.health.nsw.gov.au/health-practitioners/login",
  ["nsw"]:
    "https://www.safescript.health.nsw.gov.au/health-practitioners/login",

  ["queensland"]:
    "https://ndeqldprdip1.b2clogin.com/ndeqldprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=89c311a5-5bc1-4fcc-b196-f1bac048f71a&scope=https%3A%2F%2Fndeqldprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.qscript.health.qld.gov.au%2Fhome&client-request-id=53031aed-3718-4ff4-b44b-a3b821f18bdf&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.39.0&client_info=1&code_challenge=cDfyn-Ca0xfW1TtkfF2pNvZMlWo_TV_uIR709qz0qbg&code_challenge_method=S256&nonce=c5aa1efb-e9cf-46a0-a9d5-67ea127d9e33&state=eyJpZCI6IjUzZDk0YTM3LWNjZGEtNGE4Zi1hODc5LTFlMTBkNThkMDJiMiIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["qld"]:
    "https://ndeqldprdip1.b2clogin.com/ndeqldprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=89c311a5-5bc1-4fcc-b196-f1bac048f71a&scope=https%3A%2F%2Fndeqldprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.qscript.health.qld.gov.au%2Fhome&client-request-id=53031aed-3718-4ff4-b44b-a3b821f18bdf&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.39.0&client_info=1&code_challenge=cDfyn-Ca0xfW1TtkfF2pNvZMlWo_TV_uIR709qz0qbg&code_challenge_method=S256&nonce=c5aa1efb-e9cf-46a0-a9d5-67ea127d9e33&state=eyJpZCI6IjUzZDk0YTM3LWNjZGEtNGE4Zi1hODc5LTFlMTBkNThkMDJiMiIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",

  ["south australia"]:
    "https://ndesauprdip1.b2clogin.com/ndesauprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=4ab1466d-e160-47b5-bdbc-c069c6519626&scope=https%3A%2F%2Fndesauprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.scriptcheck.sa.gov.au%2Fhome&client-request-id=6f00b98f-4e50-4a75-b6d8-345e4c185d6f&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=P0a3VZruR7cC3Za6CRR7Z1KLpTCTCB1PAqsZE3PvQCs&code_challenge_method=S256&nonce=b864ffd6-e13b-47f6-89e6-2b0ccdf2496e&state=eyJpZCI6IjU4NWQwNDFhLWJhNTQtNGFkZi05NDYwLWFiMDliMmI2MWI3NSIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["sa"]:
    "https://ndesauprdip1.b2clogin.com/ndesauprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=4ab1466d-e160-47b5-bdbc-c069c6519626&scope=https%3A%2F%2Fndesauprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.scriptcheck.sa.gov.au%2Fhome&client-request-id=6f00b98f-4e50-4a75-b6d8-345e4c185d6f&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=P0a3VZruR7cC3Za6CRR7Z1KLpTCTCB1PAqsZE3PvQCs&code_challenge_method=S256&nonce=b864ffd6-e13b-47f6-89e6-2b0ccdf2496e&state=eyJpZCI6IjU4NWQwNDFhLWJhNTQtNGFkZi05NDYwLWFiMDliMmI2MWI3NSIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",

  ["tasmania"]:
    "https://ndetasprdb2c.b2clogin.com/ndetasprdb2c.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=88eed1f1-5521-4755-88f8-b1ba5ed2da74&scope=https%3A%2F%2Fndetasprdb2c.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.tasscript.health.tas.gov.au%2Fhome&client-request-id=6d1bbc57-c198-454a-a600-e77b175bb0eb&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=jFrhGmOehHqn8-45zCv51yQ8KrEDVeOd7kmRpQnK4lU&code_challenge_method=S256&nonce=2dc5ea98-e40c-4295-ba4d-dbdc83ef2cc1&state=eyJpZCI6ImQ0YzJjNTI5LTZmOGYtNDhhNy04ZmI0LTA2Yzc5OTk1YzE0OCIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["tas"]:
    "https://ndetasprdb2c.b2clogin.com/ndetasprdb2c.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=88eed1f1-5521-4755-88f8-b1ba5ed2da74&scope=https%3A%2F%2Fndetasprdb2c.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.tasscript.health.tas.gov.au%2Fhome&client-request-id=6d1bbc57-c198-454a-a600-e77b175bb0eb&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=jFrhGmOehHqn8-45zCv51yQ8KrEDVeOd7kmRpQnK4lU&code_challenge_method=S256&nonce=2dc5ea98-e40c-4295-ba4d-dbdc83ef2cc1&state=eyJpZCI6ImQ0YzJjNTI5LTZmOGYtNDhhNy04ZmI0LTA2Yzc5OTk1YzE0OCIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",

  ["australian capital territory"]:
    "https://ndeactprdip1.b2clogin.com/ndeactprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=c426704a-fe0c-4969-9d27-583d35835f58&scope=https%3A%2F%2Fndeactprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.canberrascript.act.gov.au%2Fhome&client-request-id=59d0a36e-a225-4707-a420-da82e664da7b&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=RJyAgnMpnaFLGoO3tMVxGsJ-lWXKsxF9NP_gmx332jc&code_challenge_method=S256&nonce=cbe5bfe2-34ba-4bb3-80fa-e66e965e52e5&state=eyJpZCI6IjE2YTQwYTk2LTBlZDQtNDk2My1iYjk4LTIwOTNlN2RmZjJiYyIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["act"]:
    "https://ndeactprdip1.b2clogin.com/ndeactprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=c426704a-fe0c-4969-9d27-583d35835f58&scope=https%3A%2F%2Fndeactprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.canberrascript.act.gov.au%2Fhome&client-request-id=59d0a36e-a225-4707-a420-da82e664da7b&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=RJyAgnMpnaFLGoO3tMVxGsJ-lWXKsxF9NP_gmx332jc&code_challenge_method=S256&nonce=cbe5bfe2-34ba-4bb3-80fa-e66e965e52e5&state=eyJpZCI6IjE2YTQwYTk2LTBlZDQtNDk2My1iYjk4LTIwOTNlN2RmZjJiYyIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",

  ["northern territory"]:
    "https://ndenteprdip1.b2clogin.com/ndenteprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=2ed49083-d703-4f13-b86a-0368846e1fcf&scope=https%3A%2F%2Fndenteprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.ntscript.nt.gov.au%2Fhome&client-request-id=3bbd5222-d60c-49a2-a2fa-068d50e44e53&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=Hm7DkxiUNKYaOgSx3cB4DGoWik3yVLNHTeTljxXIu-0&code_challenge_method=S256&nonce=5d292aa5-7460-4c9b-9c98-2405873aa825&state=eyJpZCI6Ijk5ZWU1MjQxLTY2ZmYtNDE3Yy04Nzg4LTU2MWQzYWNlN2ZiYiIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
  ["nt"]:
    "https://ndenteprdip1.b2clogin.com/ndenteprdip1.onmicrosoft.com/b2c_1a_v1_multiple_account_sign_in/oauth2/v2.0/authorize?client_id=2ed49083-d703-4f13-b86a-0368846e1fcf&scope=https%3A%2F%2Fndenteprdip1.onmicrosoft.com%2Frtpmwebapi%2Fapi.access%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fhp.ntscript.nt.gov.au%2Fhome&client-request-id=3bbd5222-d60c-49a2-a2fa-068d50e44e53&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.38.0&client_info=1&code_challenge=Hm7DkxiUNKYaOgSx3cB4DGoWik3yVLNHTeTljxXIu-0&code_challenge_method=S256&nonce=5d292aa5-7460-4c9b-9c98-2405873aa825&state=eyJpZCI6Ijk5ZWU1MjQxLTY2ZmYtNDE3Yy04Nzg4LTU2MWQzYWNlN2ZiYiIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D",
};

export const provinceAbbreviation: { [key: string]: string } = {
  ["western australia"]: "WA",
  ["wa"]: "WA",

  ["victoria"]: "VIC",
  ["vic"]: "VIC",

  ["new south wales"]: "NSW",
  ["nsw"]: "NSW",

  ["queensland"]: "QLD",
  ["qld"]: "QLD",

  ["south australia"]: "SA",
  ["sa"]: "SA",

  ["tasmania"]: "TAS",
  ["tas"]: "TAS",

  ["australian capital territory"]: "ACT",
  ["act"]: "ACT",

  ["northern territory"]: "NT",
  ["nt"]: "NT",
};

export const questionnaireQuestions: { [key: string]: string } = {
  Condition: "What is your condition? ",
  What_condition_or_symptom_are_you_having_issues_wi:
    "What condition or symptom are you having issues with?",
  Are_you_planning_to_have_children_in_the_near_futu:
    "Are you planning to have children in the near future?",
  Do_you_suffer_from_any_cardiovascular_diseases_in:
    "Do you suffer from any cardiovascular diseases, including irregular heartbeat (arrhythmia)?",
  Do_you_have_an_addiction_to_any_psychoactive_subst:
    "Do you have an addiction to any psychoactive substances and/or drugs, including alcohol, but excluding nicotine and caffeine?",
  What_was_your_gender_at_birth: "What was your gender at birth?",
  Please_add_the_first_medication_treatment_or_ther:
    "Please add the first medication, treatment or therapy you trialled.",
  Please_add_the_second_medication_treatment_or_the:
    "Please add the second medication, treatment or therapy you trialled.",
  Have_you_discussed_other_treatment_options_with_yo:
    "Have you discussed other treatment with your doctor? Including medical and conservatve therapies.",
  Knowing_the_alternative_management_options_do_you:
    "Knowing the alternative management options, do you still want trial Alternative Medicine as a treatment option for your condition?",
  Do_you_suffer_from_psychosis_bipolar_disorder_or:
    "Do you suffer from psychosis, bipolar disorder or schizophrenia?",
};

export const UserActions: { [key: string]: string } = {
  ADMITTED: "ADMITTED",
  REJECTED: "REJECTED",
  JOINED: "JOINED",
  ENDED: "ENDED",
  AWAY: "AWAY",
  CONFIRMED: "CONFIRMED",
  COMPLETED: "COMPLETED",
  REDIRECTED: "REDIRECTED",
  REACHED: "REACHED",
  ONLINE: "ONLINE",
  RETURNED: "RETURNED",
  TIMEOUT: "TIMEOUT",
  NOSHOW: "NOSHOW",
  TECHISSUE: "TECH_ISSUE",
  BUFFER_PERIOD: "BUFFER_PERIOD",
  TIMER_WARNING: "TIMER_WARNING",
  LOGIN: "LOGIN",
  LOGOUT: "LOGOUT",
  DIAGNOSIS_CREATED: "DIAGNOSIS_CREATED",
  DIAGNOSIS_UPDATED: "DIAGNOSIS_UPDATED",
  DIAGNOSIS_VIEWED: "DIAGNOSIS_VIEWED",
  DIAGNOSIS_SUBMITTED: "DIAGNOSIS_SUBMITTED",
};

// Utility function to get user's IP address (best effort)
export const getUserIP = async (): Promise<string | undefined> => {
  try {
    // Try to get IP from a public service
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.warn("Could not fetch user IP:", error);
    return undefined;
  }
};

// Utility function to get user agent
export const getUserAgent = (): string => {
  return navigator.userAgent;
};

// Utility function to generate a session ID
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

type LetterTable = {
  tradeName: string;
  strength: string;
};

export const supplyLetterTable: LetterTable[] = [
  {
    tradeName: "29-09/18-10/20-11/01-12/15-01",
    strength: "29% THC",
  },
  {
    tradeName: "15-02/19-03/08-04/11-05/30-06",
    strength: "29% THC",
  },
  {
    tradeName: "03-10/10-11/15-12/18-01/20-02",
    strength: "29% THC",
  },
  {
    tradeName: "23-03/17-04/14-05/28-06/09-07",
    strength: "29% THC",
  },
  {
    tradeName: "19-02/31-03/16-04/05-05/24-06",
    strength: "22% THC",
  },
  {
    tradeName: "14-06/26-07/19-08/12-09/21-10",
    strength: "29% THC",
  },
  {
    tradeName: "26-11/09-12/29-01/22-02/10-03",
    strength: "29% THC",
  },
  {
    tradeName: "08-02/24-03/11-04/22-05/09-06",
    strength: "29% THC",
  },
  {
    tradeName: "07-08/13-09/09-10/30-11/03-12",
    strength: "22% THC",
  },
  {
    tradeName: "19-04/03-05/11-06/18-07/26-08",
    strength: "29% THC",
  },
  {
    tradeName: "25-10/05-11/11-12/21-01/19-02",
    strength: "22% THC",
  },
  {
    tradeName: "21-08/30-09/12-10/24-11/05-12",
    strength: "29% THC",
  },
  {
    tradeName: "16-03/15-04/16-05/23-06/28-07",
    strength: "22% THC",
  },
  {
    tradeName: "04-05/27-06/05-07/28-08/14-09",
    strength: "22% THC",
  },
  {
    tradeName: "20-01/28-02/05-03/27-04/09-05",
    strength: "22% THC",
  },
  {
    tradeName: "15-07/20-08/03-09/27-10/29-11",
    strength: "22% THC",
  },
  {
    tradeName: "25-01/12-02/31-03/02-04/28-05",
    strength: "29% THC",
  },
];

// apps/web/public/assets/DrAnjum.png
export const doctorSignatureMapping = {
  "Dr. Gazale": "/assets/DrGazale.png",
  "Dr TJ Nguyen": "/assets/Drnguyene.png",
  "Dr J. Lavett": "/assets/Drjameslavett.png",
  "Dr H. Anjum": "/assets/DrAnjum.png",
  "Dr. Anjum": "/assets/DrAnjum.png",
};

/**
 * Extracts the diagnosis (Condition) from patient questionnaire data
 * @param patientHistory Array of patient history items including questionnaires
 * @returns The condition/diagnosis string or undefined if not found
 */
export const extractDiagnosisFromQuestionnaire = (
  patientHistory: (QuestionnaireShape | TreatmentPlanHistory | PatientOrder | HealthCheckShape)[]
): string | undefined => {
  // Find the most recent questionnaire in the patient history
  const questionnaire = patientHistory
    .filter((item): item is QuestionnaireShape => item.type === 'questionnaire')
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())[0];

  if (!questionnaire?.data) {
    return undefined;
  }

  // Find the "Condition" question in the questionnaire data
  const conditionAnswer = questionnaire.data.find(q => q.question === 'Condition');

  return conditionAnswer?.answers?.trim() || undefined;
};
