import { Outlet, ReactLocation, Route, Router, MakeGenerics } from '@tanstack/react-location'
//import { Navigate, Outlet, ReactLocation, Route, Router, MakeGenerics } from '@tanstack/react-location'

import DocPortal from './components/doc-portal'
import Login from './components/login'
import Shell from './components/shell'
import RouteTracker from './utils/route-tracker'
import { AuthProvider } from './hooks/auth-provider'
import ErrorBoundary from './components/error/errorBoundary'
import EmailConfirmation from './components/doc-portal/emailConfirmation'
import { CssBaseline } from '@mui/material'
import NotFound from './components/error/notFound'
import Consultation from './components/patient-portal/consultation'
import { PatientProvider } from './hooks/patient-provider'
import { MeetingProvider } from './hooks/meeting-provider'
import PatientHistory from './components/doc-portal/patientHistory'
import DoctorConsultation from './components/doc-portal/doctor-consultation'
import Scheduler from './components/scheduler/patient'
import AdminScheduler from './components/scheduler/admin'
import HomePage from './components/doc-portal/home'
import WaitingRoom from './components/patient-portal/waiting-room'
import PatientQueueView from './components/doc-portal/patient-queue-view'
import DoctorRegistration from './components/doc-portal/doctor-registration'
import PatientHistoricalData from './components/doc-portal/patient-historical-data'
import { SnackbarProvider } from 'notistack'
import { HistoryProvider } from './hooks/history-provider'
import PatientOnlineQueue from './components/doc-portal/patient-queue-online'
import RebookNoShowPatient from './components/scheduler/rebook'
import ChatWindow from './components/doc-portal/chat'
import PatientChatWindow from './components/patient-portal/chat'
import DocterActivity from './components/doc-portal/docter-activity'
import RootRedirect from './components/root-redirect'
import ModerationDashboard from './components/admin/moderation/ModerationDashboard'
import AdminBookingInterface from './components/admin/AdminBookingInterface'
import MedecineRegister from './components/dispatch/medecine-register'
import PatientReports from './components/doc-portal/patient-reports'
import PatientReport from './components/admin/patient-report'


// Define a generics type to ensure TypeScript knows token is a string
type LocationGenerics = MakeGenerics<{
  Search: {
    token: string; // Explicitly type token as string
  };
}>;

// Create a new ReactLocation instance with custom parsing
const location = new ReactLocation<LocationGenerics>({
  parseSearch: (searchStr) => {
    const params = new URLSearchParams(searchStr);
    const token = params.get("token");
    // Clean the token by removing any quotes
    const cleanedToken = token ? token.replace(/['"]/g, '') : "";
    return {
      token: cleanedToken, // Keep it as a string, provide default if null
    };
  },
  stringifySearch: (search) => {
    const params = new URLSearchParams();
    if (search.token) {
      params.set("token", search.token);
    }
    return params.toString();
  },
});

const routes: Route<LocationGenerics>[] = [
  {
    path: '/medicines-register',
    element: <MedecineRegister/>
  },
  {
    path: '/consultation',
    element: <MeetingProvider><Consultation /></MeetingProvider>
  },
  {
    path: '/waiting-room',
    element: <MeetingProvider><WaitingRoom /></MeetingProvider>
  },
  {
    path: '/schedule',
    element: <Scheduler />
  },
  {
    path: '/rebook/:id',
    element: <RebookNoShowPatient />
  },
  {
    path: '/patient-chat',
    element: <PatientChatWindow />,
  },
  {
    path: '',
    element: (
      <AuthProvider>
        <MeetingProvider>
          <PatientProvider>
            <HistoryProvider>
              <RouteTracker />
              <ErrorBoundary>
                <Shell />
              </ErrorBoundary>
            </HistoryProvider>
          </PatientProvider>
        </MeetingProvider>
      </AuthProvider>
    ),
    children: [
      {
        path: '/doc',
        element: <DocPortal />
      },
      {
        path: '/email-confirmation',
        element: <EmailConfirmation />
      },
      {
        path: '/login',
        element: <Login />
      },
      {
        path: '/home',
        element: <HomePage />
      },
      {
        path: '/patient-history',
        element: <PatientHistory />
      },
      {
        path: '/doctor-consultation',
        element: <DoctorConsultation />
      },
      {
        path: '/schedule-admin',
        element: <AdminScheduler />
      },
      {
        path: '/patient-queue',
        element: <PatientQueueView />
      },
      {
        path: '/online-patients',
        element: <PatientOnlineQueue />
      },
      {
        path: '/patient/history',
        element: <PatientHistoricalData />
      },
      {
        path: '/',
        element: <RootRedirect />
      },
      {
        path: '/doctor-registration',
        element: <DoctorRegistration />
      },
      {
        path: '/chat',
        element: <ChatWindow />
      },
      {
        path: '/drActivity',
        element: <DocterActivity />
        },
      {
        path: '/admin/moderation',
        element: <ModerationDashboard />
      },
      {
        path: '/admin/bookings',
        element: <AdminBookingInterface />
      },
      {
        path: '/patient/reports',
        element: <PatientReports />
      },
      {
        path: '/patient/report/:id',
        element: <PatientReport />
      },
      {
        path: '*',
        element: <NotFound />
      }
    ],
  },
]

function App() {
  return (
    <>
      <CssBaseline />
      <SnackbarProvider anchorOrigin={{ horizontal: 'right', vertical: 'top' }}>
        <Router location={location} routes={routes}>
          <Outlet />
        </Router>
      </SnackbarProvider>
    </>
  )
}

export default App
