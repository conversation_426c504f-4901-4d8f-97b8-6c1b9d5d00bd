# Timer Persistence Implementation Documentation

## Overview

This document describes the implementation of persistent consultation timers in the doctor consultation component. The system ensures that all timer states are preserved across page refreshes and browser sessions using localStorage, providing a seamless experience for doctors during consultations.

## Key Features

### 1. Persistent Timer States
- All consultation timers persist across page refreshes
- Timer states are automatically restored with accurate remaining time
- Consultation-specific storage prevents data leakage between patients
- Automatic cleanup when transitioning between patients

### 2. Supported Timer Types
- **Main Consultation Timer**: Overall consultation duration (default 6 minutes)
- **Final Countdown**: 60-second countdown at 1 minute before consultation end
- **Buffer Countdown**: 60-second post-consultation buffer period
- **Patient Dropout Countdown**: 30-second countdown when patient leaves
- **Patient Not Joined Countdown**: 30-second countdown when patient hasn't joined
- **Pause/Resume States**: Typing and focus-based timer pausing

### 3. Automatic State Management
- Real-time synchronization to localStorage on every timer update
- Intelligent restoration of timer states on component mount
- Proper handling of expired timers and edge cases
- Comprehensive cleanup on patient transitions

## Implementation Architecture

### Core Components

#### 1. Timer Persistence Utilities (`timer-persistence.ts`)

```typescript
interface ConsultationTimerState {
  consultationId: string;        // Unique consultation identifier
  doctorId: string;             // Doctor's access ID
  consultation: {               // Main consultation timer info
    startTime: number;
    durationMinutes: number;
    earlyWarningShown: boolean;
    finalCountdownTriggered: boolean;
  };
  countdowns: {                 // Individual countdown states
    finalCountdown: CountdownState;
    bufferCountdown: CountdownState;
    patientDropout: CountdownState;
    patientNotJoined: CountdownState;
  };
  pauseState: {                 // Pause/resume management
    isPaused: boolean;
    pausedAt: number;
    activeTriggers: string[];
    prePauseValues: object;
  };
  lastUpdated: number;          // Timestamp for state validation
  version: string;              // Version compatibility
}
```

#### 2. Storage Strategy

**Key Pattern**: `consultation_timers_${consultationId}`
- Uses patient ID (token) as unique consultation identifier
- Scoped storage prevents conflicts between different consultations
- Automatic cleanup removes stale data when switching patients

#### 3. State Synchronization

**Real-time Updates**: Every timer change is immediately persisted
- Countdown value changes every second
- Pause/resume state changes
- Timer start/stop events
- Warning and milestone triggers

### Integration Points

#### 1. Timer Initialization (`initializeTimerState`)
```typescript
const initializeTimerState = () => {
  // Load existing state or create new one
  const existingState = loadTimerState(token);
  
  if (existingState && !isConsultationExpired(existingState)) {
    timerState.current = existingState;
  } else {
    timerState.current = createInitialTimerState(token, doctorId, duration);
  }
}
```

#### 2. Timer Restoration (`useEffect` on component mount)
- Automatically restores active countdowns with correct remaining time
- Recalculates elapsed time since last update
- Resumes intervals for active timers
- Restores pause states and active triggers

#### 3. Cleanup on Patient Transitions
```typescript
// In inviteNextPatient function
clearTimerState(token);                    // Clear current consultation
clearAllTimerStates();                     // Clear all consultations
timerState.current = null;                 // Reset component state
timerStateInitialized.current = false;    // Allow re-initialization
```

## Key Functions

### Utility Functions

#### `saveTimerState(state: ConsultationTimerState)`
- Persists timer state to localStorage with metadata
- Adds timestamp and version information
- Handles storage errors gracefully

#### `loadTimerState(consultationId: string)`
- Retrieves timer state from localStorage
- Validates version compatibility
- Returns null for invalid or missing states

#### `calculateRemainingTime(originalSeconds: number, startTime: number)`
- Calculates accurate remaining time based on elapsed time
- Handles time calculations for timer restoration
- Ensures non-negative values

#### `clearTimerState(consultationId: string)`
- Removes specific consultation timer data
- Used when ending current consultation

#### `clearAllTimerStates()`
- Removes all consultation timer data
- Used when transitioning between patients or leaving consultation area

### Component Integration Functions

#### `updateCountdownInState(countdownType, updates)`
- Updates specific countdown state in timer state
- Immediately persists changes to localStorage
- Maintains state consistency

#### `updatePauseInState(pauseUpdates)`
- Updates pause state information
- Tracks active triggers and pre-pause values
- Enables proper pause/resume functionality

## Timer Lifecycle

### 1. Consultation Start
1. `initializeTimerState()` called in `handleDrJoinedMeeting`
2. Existing state loaded or new state created
3. Main consultation timers set up with remaining time
4. Warning and countdown timers scheduled appropriately

### 2. During Consultation
1. Countdown intervals update every second
2. Each update persisted to localStorage via `updateCountdownInState`
3. Pause/resume events update pause state immediately
4. Timer state reflects current consultation status

### 3. Page Refresh
1. Component mounts and restoration `useEffect` runs
2. Timer state loaded from localStorage
3. Active countdowns restored with correct remaining time
4. Intervals restarted for active timers
5. Pause states and triggers restored

### 4. Patient Transition
1. `inviteNextPatient` called
2. Current consultation timer state cleared
3. All timer states cleared before navigation
4. Component state reset for next patient

## Error Handling

### Expired Timer Detection
```typescript
const isConsultationExpired = (state: ConsultationTimerState): boolean => {
  const consultationDurationMs = state.consultation.durationMinutes * 60 * 1000;
  const elapsed = Date.now() - state.consultation.startTime;
  return elapsed >= consultationDurationMs;
};
```

### Storage Error Handling
- All localStorage operations wrapped in try-catch blocks
- Graceful degradation when storage is unavailable
- Console logging for debugging storage issues

### Version Compatibility
- Timer state includes version field for future compatibility
- Automatic cleanup of incompatible versions
- Fallback to new state creation when version mismatch

## Testing Scenarios

### 1. Basic Timer Persistence
- Start consultation, refresh page
- Verify timers continue from correct remaining time
- Check that countdown displays match internal state

### 2. Countdown Restoration
- Start final countdown, refresh page
- Verify countdown resumes with correct remaining time
- Ensure countdown completes properly after restoration

### 3. Pause State Restoration
- Pause timers by typing, refresh page
- Verify pause state is restored
- Check that timers remain paused until activity ends

### 4. Patient Transition Cleanup
- Switch between patients
- Verify localStorage is cleaned between transitions
- Ensure no timer data leaks between consultations

### 5. Expired Timer Handling
- Let consultation time expire, refresh page
- Verify expired timers are cleaned up
- Check that consultation ends appropriately

## Configuration

### Storage Keys
- Primary: `consultation_timers_${consultationId}`
- Pattern: `consultation_timers_*` for cleanup operations
- Doctor settings: `doctorConsultationDuration_${doctorId}` (unchanged)

### Timer Defaults
- Final countdown: 60 seconds
- Buffer countdown: 60 seconds
- Patient dropout: 30 seconds
- Patient not joined: 30 seconds
- Consultation duration: 6 minutes (configurable per doctor)

### Version Information
- Current version: "1.0.0"
- Stored in timer state for compatibility checking
- Automatic cleanup on version mismatch

## Benefits

### For Doctors
- Seamless experience across page refreshes
- No loss of consultation progress
- Accurate timer information at all times
- Consistent behavior regardless of browser issues

### For System Reliability
- Robust state management
- Automatic cleanup prevents storage bloat
- Error handling ensures graceful degradation
- Version compatibility for future updates

### For Development
- Modular utility functions
- Clear separation of concerns
- Comprehensive error handling
- Extensive logging for debugging

## Implementation Files

### Modified Files
1. **`apps/web/src/components/doc-portal/doctor-consultation.tsx`**
   - Added timer persistence integration
   - Modified timer initialization logic
   - Updated countdown functions with state synchronization
   - Added pause/resume state persistence
   - Implemented cleanup on patient transitions
   - Added timer restoration on component mount

2. **`apps/web/src/components/doc-portal/utils/timer-persistence.ts`** (New)
   - Core timer persistence utilities
   - Type definitions for timer state
   - Storage management functions
   - Time calculation utilities
   - State update helpers

### Code Changes Summary
- **Added**: 200+ lines of timer persistence logic
- **Modified**: Existing timer functions to integrate with persistence
- **Enhanced**: Error handling and edge case management
- **Improved**: State management and cleanup procedures

## Usage Instructions

### For Developers

#### Adding New Timer Types
1. Update `ConsultationTimerState` interface in `timer-persistence.ts`
2. Add countdown state to `countdowns` object
3. Implement restoration logic in component `useEffect`
4. Add cleanup logic in transition functions

#### Debugging Timer Issues
1. Use browser console: `debugTimerStates()` function available globally
2. Check localStorage: Look for keys starting with `consultation_timers_`
3. Monitor console logs for timer state changes and errors
4. Verify timer state persistence across page refreshes

#### Testing Timer Persistence
1. Start a consultation and let timers run
2. Refresh the page during active countdowns
3. Verify timers resume with correct remaining time
4. Test patient transitions to ensure cleanup
5. Check edge cases like expired timers and storage errors

### For QA Testing

#### Test Cases
1. **Basic Persistence**: Refresh during consultation, verify timer continuity
2. **Countdown Restoration**: Refresh during final countdown, check accuracy
3. **Pause State**: Pause timers, refresh, verify pause is maintained
4. **Patient Switching**: Switch patients, verify no timer data leakage
5. **Storage Limits**: Test with localStorage near capacity
6. **Network Issues**: Test with poor connectivity during timer operations

## Future Enhancements

### Potential Improvements
1. **Server-side Synchronization**: Sync timer states with backend for multi-device support
2. **Timer Analytics**: Track timer usage patterns for optimization
3. **Advanced Pause Logic**: More sophisticated pause triggers and conditions
4. **Timer Customization**: Per-doctor timer preferences and configurations
5. **Backup Strategies**: Alternative storage methods for localStorage failures

### Monitoring Considerations
1. **Storage Usage**: Monitor localStorage usage to prevent quota issues
2. **Performance Impact**: Track impact of frequent localStorage updates
3. **Error Rates**: Monitor storage operation failures
4. **User Experience**: Collect feedback on timer behavior and accuracy
