import { TOPICS } from '../../helpers/topics';
import { logger } from '../../config/logger';
import { Slot } from '../../helpers/range';
import { ZohoAuth, zohoLeadURL, zohoContactURL } from '../../helpers/zoho';
import { ApiError } from '../../utils/ApiError';
import { catchAll } from '../../utils/catchAll';
import { db } from '../../utils/db';
import axios, { AxiosError } from 'axios';
import { PoolClient } from 'pg';
import { RequestHandler } from 'express';
import httpStatus from 'http-status';
import { WebSocketManager } from '../../helpers/webSocketManager';
import { DateTime } from 'luxon';
import config from '../../config';
import { UrlEncryptionHelper } from '../../helpers/encryption';
import { verifyNoShowPatient } from '../common';
import { ZohoTreatmentPlan } from '../../types';

// Interface for source slot data in aggregated slots
interface SourceSlot {
  range_id: string;
  slot_id: string;
  doctorID: string;
  remaining: number;
}

// Helper function to randomly assign a doctor for aggregated slots
const assignRandomDoctor = async (client: PoolClient, slotData: { range_id?: string; id?: string; source_slots?: unknown }, useNoShowPool: boolean) => {
  // Check if this is an aggregated slot (range_id starts with 'aggregated_')
  if (!slotData.range_id || !slotData.range_id.toString().startsWith('aggregated_')) {
    // This is a regular slot, return the original range_id and slot_id
    return {
      range_id: slotData.range_id,
      slot_id: slotData.id,
      doctorID: null // Will be determined from the range table
    };
  }

  // For aggregated slots, we need to find the source_slots from the slot data
  // The frontend should pass the source_slots information in the slot data
  if (!slotData.source_slots) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Missing source slot information for aggregated booking');
  }

  // Parse source_slots if it's a string
  const sourceSlots: SourceSlot[] = typeof slotData.source_slots === 'string'
    ? JSON.parse(slotData.source_slots)
    : (slotData.source_slots as SourceSlot[]);

  // Filter source slots that have availability
  const availableSlots = sourceSlots.filter((slot: SourceSlot) => slot.remaining > 0);

  if (availableSlots.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'No available slots for this time');
  }

  // Randomly select one of the available slots
  const randomIndex = Math.floor(Math.random() * availableSlots.length);
  const selectedSlot = availableSlots[randomIndex];

  // Verify the slot still has availability in the database
  const checkSlotQuery = useNoShowPool
    ? `SELECT id, range_id, "noShowRemaining" as remaining FROM Slot WHERE id = $1 AND "noShowRemaining" > 0`
    : `SELECT id, range_id, remaining FROM Slot WHERE id = $1 AND remaining > 0`;

  const slotCheck = await client.query(checkSlotQuery, [selectedSlot.slot_id]);

  if (slotCheck.rows.length === 0) {
    // This slot is no longer available, try another one
    const otherSlots = availableSlots.filter((slot: SourceSlot) => slot.slot_id !== selectedSlot.slot_id);
    if (otherSlots.length === 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'No available slots for this time');
    }

    // Recursively try with remaining slots
    //return assignRandomDoctor(client, { ...slotData, source_slots: otherSlots }, useNoShowPool);
  }

  return {
    range_id: selectedSlot.range_id,
    slot_id: selectedSlot.slot_id,
    doctorID: selectedSlot.doctorID
  };
};

// Interface for Zoho contact data structure
interface ZohoContact {
  id: string;
  Email?: string;
  First_Name?: string;
  Last_Name?: string;
  Full_Name?: string;
  Phone?: string;
  Mobile?: string;
  Created_Time: string;
  Modified_Time?: string;
  Member_Status?: string;
  Member_ID1?: string;
  Doctor_Notes?: string;
  Mental_Health_Supporting_Documentation?: string;
  Consulting_Doctor?: string;
  Supply_Date_1?: string;
  Supply_Expiration?: string;
  Strength_Concentration?: string;
  Total_Qty_22_1?: string;
  Total_Qty_29_1?: string;
  Dose_Per_Day_22_1?: string;
  Dose_Per_Day_29_1?: string;
  Number_of_Repeats_1?: string;
  Next_Repeat_1?: string;
  Dispensing_Interval_Period_1?: string;
  Maximum_Doses_per_Day_22_1?: string;
  Maximum_Doses_per_Day_29_1?: string;
  Pouch_Count_22_1?: string;
  Pouch_Count_29_1?: string;
}

// Interface for Zoho lead data structure (subset of fields used)
interface ZohoLead {
  id: string;
  Email?: string;
  Created_Time: string;
  Member_Status?: string;
}

// TODO: Needs cleaning and avoid Repetition.

export const getPatientBookingHistory: RequestHandler = catchAll(async (req, res) => {
  const patientId = req.params.patientId;
  const client = await db.connect();

  try {
    const bookingHistoryQuery = `
      SELECT
        ps.patient_id,
        ps.range_id,
        ps.slot_id,
        ps."queueType",
        ps."bookedByAdminId",
        ps."bookingType",
        ps."createdAt",
        ps."updatedAt",
        r.date AS range_date,
        s.slot AS slot_details,
        dr.name AS admin_name,
        dr.email AS admin_email
      FROM PatientSlot ps
      JOIN Range r ON ps.range_id = r.id
      JOIN Slot s ON ps.slot_id = s.id
      LEFT JOIN Dr dr ON ps."bookedByAdminId" = dr."accessID"
      WHERE ps.patient_id = $1
      ORDER BY ps."createdAt" DESC
    `;

    const result = await client.query(bookingHistoryQuery, [patientId]);
    res.status(200).json(result.rows);
  } catch (error) {
    logger.error('Error fetching booking history:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch booking history');
  } finally {
    client.release();
  }
});

export const getAdminBookings: RequestHandler = catchAll(async (req, res) => {
  const { adminId, startDate, endDate, period, page = '1', limit = '5' } = req.query;
  const client = await db.connect();

  try {
    let dateFilter = '';
    const queryParams: (string | number)[] = [];
    let paramIndex = 1;

    // Build date filter based on period or custom dates
    if (period === 'today') {
      dateFilter = `AND TO_DATE(r.date, 'YYYY-MM-DD') = CURRENT_DATE`;
    } else if (period === 'week') {
      dateFilter = `AND TO_DATE(r.date, 'YYYY-MM-DD') >= DATE_TRUNC('week', CURRENT_DATE) AND TO_DATE(r.date, 'YYYY-MM-DD') < DATE_TRUNC('week', CURRENT_DATE) + INTERVAL '7 days'`;
    } else if (startDate && endDate) {
      dateFilter = `AND TO_DATE(r.date, 'YYYY-MM-DD') >= TO_DATE($${paramIndex}, 'YYYY-MM-DD') AND TO_DATE(r.date, 'YYYY-MM-DD') <= TO_DATE($${paramIndex + 1}, 'YYYY-MM-DD')`;
      queryParams.push(startDate as string, endDate as string);
      paramIndex += 2;
    }

    // Build admin filter
    let adminFilter = '';
    if (adminId && adminId !== 'all') {
      adminFilter = `AND ps."bookedByAdminId" = $${paramIndex}`;
      queryParams.push(adminId as string);
      paramIndex++;
    }

    // Parse pagination parameters
    const pageNum = parseInt(page as string, 10) || 1;
    const limitNum = parseInt(limit as string, 10) || 5;
    const offset = (pageNum - 1) * limitNum;

    // First, get the total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM PatientSlot ps
      JOIN Range r ON ps.range_id = r.id
      JOIN Slot s ON ps.slot_id = s.id
      LEFT JOIN Dr dr ON ps."bookedByAdminId" = dr."accessID"
      WHERE ps."bookingType" = 'admin'
      ${dateFilter}
      ${adminFilter}
    `;

    const countResult = await client.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total, 10);
    const totalPages = Math.ceil(total / limitNum);

    // Add pagination parameters
    queryParams.push(limitNum, offset);
    const limitParam = paramIndex;
    const offsetParam = paramIndex + 1;

    const adminBookingsQuery = `
      SELECT
        ps.patient_id,
        ps.range_id,
        ps.slot_id,
        ps."queueType",
        ps."bookedByAdminId",
        ps."bookingType",
        ps."createdAt",
        ps."updatedAt",
        r.date AS range_date,
        s.slot AS slot_details,
        dr.name AS admin_name,
        dr.email AS admin_email
      FROM PatientSlot ps
      JOIN Range r ON ps.range_id = r.id
      JOIN Slot s ON ps.slot_id = s.id
      LEFT JOIN Dr dr ON ps."bookedByAdminId" = dr."accessID"
      WHERE ps."bookingType" = 'admin'
      ${dateFilter}
      ${adminFilter}
      ORDER BY ps."createdAt" DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;

    const result = await client.query(adminBookingsQuery, queryParams);

    // Return paginated response
    res.status(200).json({
      bookings: result.rows,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    logger.error('Error fetching admin bookings:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch admin bookings');
  } finally {
    client.release();
  }
});

export const postPatientBooking: RequestHandler = catchAll(async (req, res) => {
  const body = req.body as Slot & { bookedByAdminId?: string };
  const leadId = req.params.leadId;
  const client = await db.connect();

  // How do I get patient email here?
  // if I get patient email, then I can ignore them.
  // for now it is not possible before we do not know the patient email at this point.
  // query Zoho isn't a good idea. Might slow us down.
  // Let us focus on the admin side.

  // Solution - React Funnel.
  // When user register we have their details in CRM already so we are good to go
  // We can then check retrieve their emails
  // with their email use the function verifyNoShowPatient(email:string) under common folder
  // check the string length
  // if > 3 book but do the reduce availability count.

  const query = `
  SELECT ps.*
        FROM PatientSlot ps
        JOIN Range r ON ps.range_id = r.id
        WHERE ps.patient_id = $1
        AND TO_DATE(r.date, 'YYYY-MM-DD') >= CURRENT_DATE`;
  const values = [leadId];
  const oldBooking = await client.query(query, values);
  await client.query('BEGIN');

  const queryHandler = async () => {
    await client.query('BEGIN');
    if (oldBooking.rows.length > 0) {
      const insertPatientBooking = `
          WITH upserted AS (
              INSERT INTO PatientSlot (patient_id, range_id, slot_id, "sameDayRebook", "queueType", "createdAt", "updatedAt")
              VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
              ON CONFLICT (patient_id, range_id)
              DO UPDATE SET
              slot_id = EXCLUDED.slot_id,
              "sameDayRebook" = EXCLUDED."sameDayRebook",
              "queueType" = EXCLUDED."queueType"
              RETURNING *
          )
          SELECT
              upserted.patient_id,
              upserted.range_id,
              upserted.slot_id,
              upserted."queueType",
              r.date AS range_date,
              s.slot AS slot_details
          FROM
              upserted
          JOIN
              Range r ON upserted.range_id = r.id
          JOIN
              Slot s ON upserted.slot_id = s.id;
      `;

      const flag = req.params.flag && req.params.flag === 'rebook' ? true : false;

      // Always check if patient is a no-show, regardless of rebooking flag
      // Get patient email to check if they are a no-show
      const patientQuery = `SELECT email FROM Patient WHERE "zohoID" = $1`;
      const patientResult = await client.query(patientQuery, [leadId]);
      const patientEmail = patientResult.rows.length > 0 ? patientResult.rows[0].email : null;

      // Check if patient is a no-show based on consultation history
      const noShowResults = patientEmail ? await verifyNoShowPatient(patientEmail) : null;
      // Only consider the patient a no-show if the most recent consultation (first in the array) is a no-show
      const useNoShowPool = noShowResults && noShowResults.length > 0 && noShowResults[0].queueTag === 'no-show';

      // Handle random doctor assignment for aggregated slots (for rebooking)
      let actualRangeIdRebook = body.range_id;
      let actualSlotIdRebook = body.id;
      let assignedDoctorIdRebook: string | null = null;

      // Check if this is an aggregated slot (starts with 'aggregated_')
      if (body.range_id && body.range_id.toString().startsWith('aggregated_')) {
        // This is an aggregated slot, we need to randomly assign a doctor
        const assignment = await assignRandomDoctor(client, body, useNoShowPool || false);
        actualRangeIdRebook = assignment.range_id;
        actualSlotIdRebook = assignment.slot_id;
        assignedDoctorIdRebook = assignment.doctorID;

        logger.info(`Random doctor assignment for rebooking patient ${leadId}: range_id=${actualRangeIdRebook}, slot_id=${actualSlotIdRebook}, doctorID=${assignedDoctorIdRebook}`);
      }

      // Determine which queue type to use based on patient history
      const queueType = useNoShowPool ? 'noShow' : 'regular';
      const values = [leadId, actualRangeIdRebook, actualSlotIdRebook, flag, queueType];


      if (!req.params.flag || req.params.flag !== 'rebook') {
        // Only check availability if not rebooking
        // Select the appropriate availability pool based on patient history
        const checkSlotRemaining = useNoShowPool
          ? `SELECT "noShowRemaining" as remaining FROM slot WHERE id = $1`
          : `SELECT remaining FROM slot WHERE id = $1`;

        const slotRemaining = await client.query(checkSlotRemaining, [actualSlotIdRebook]);

        if (
          !slotRemaining.rows.length ||
          slotRemaining.rows[0].remaining <= 0
        ) {
          logger.warn(`Failure to book for patient ${leadId} because slot ${actualSlotIdRebook} has no remaining availability`);
          throw new ApiError(httpStatus.BAD_REQUEST, 'Slot is full');
        }
      }

      const newBooking = await client.query(insertPatientBooking, values);

      if (oldBooking.rows[0].slot_id === newBooking.rows[0].slot_id) {
        if (req.params.flag === 'patient') {
          await client.query(
            `
      UPDATE PATIENT
      SET "lastCompletedForm"=$1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "zohoID"=$2`,
            ['booking', leadId],
          );
        }
        await client.query('COMMIT');
        res.status(200).send([]);
        return;
      }

      if (newBooking.rows.length > 0) {
        // Get patient email to check if they are a no-show
        const patientQuery = `SELECT email FROM Patient WHERE "zohoID" = $1`;
        const patientResult = await client.query(patientQuery, [leadId]);
        const patientEmail = patientResult.rows.length > 0 ? patientResult.rows[0].email : null;

        // Check if patient is a no-show based on consultation history
        const noShowResults = patientEmail ? await verifyNoShowPatient(patientEmail) : null;
        // Only consider the patient a no-show if the most recent consultation (first in the array) is a no-show
        const useNoShowPool = noShowResults && noShowResults.length > 0 && noShowResults[0].queueTag === 'no-show';


        // Get all slots in the range before update
        const beforeRangeQuery = `
          SELECT id, slot, remaining, "noShowRemaining"
          FROM Slot
          WHERE range_id = $1
          ORDER BY slot
        `;
        const beforeRangeSlots = await client.query(beforeRangeQuery, [newBooking.rows[0].range_id]);

        // Use explicit transaction for slot updates to ensure atomicity
        await client.query('SAVEPOINT slot_updates');

        try {

          // Determine which query to use for incrementing old slot
          let _increamentOldSlot: string;
          if (useNoShowPool) {

            _increamentOldSlot = `
              UPDATE Slot
              SET "noShowRemaining" = "noShowRemaining" + 1, "updatedAt"=CURRENT_TIMESTAMP
              WHERE id = $1
              RETURNING id, slot, remaining, "noShowRemaining";
              `;
          } else {

            _increamentOldSlot = `
              UPDATE Slot
              SET remaining = remaining + 1, "updatedAt"=CURRENT_TIMESTAMP
              WHERE id = $1
              RETURNING id, slot, remaining, "noShowRemaining";
              `;
          }

          // Execute the increment query on the old slot
          await client.query(_increamentOldSlot, [oldBooking.rows[0].slot_id]);

          // Determine which query to use for decrementing new slot
          let _decreaseNewSlot: string;
          if (useNoShowPool) {

            _decreaseNewSlot = `
              UPDATE Slot
              SET "noShowRemaining" = "noShowRemaining" - 1, "updatedAt"=CURRENT_TIMESTAMP
              WHERE id = $1 AND "noShowRemaining" > 0
              RETURNING id, slot, remaining, "noShowRemaining";
              `;
          } else {

            _decreaseNewSlot = `
              UPDATE Slot
              SET remaining = remaining - 1, "updatedAt"=CURRENT_TIMESTAMP
              WHERE id = $1 AND remaining > 0
              RETURNING id, slot, remaining, "noShowRemaining";
              `;
          }

          // Execute the decrement query on the new slot (use actual assigned slot ID)
          await client.query(_decreaseNewSlot, [actualSlotIdRebook]);


          // Get all slots in the range after update to check for unexpected changes
          const afterRangeQuery = `
            SELECT id, slot, remaining, "noShowRemaining"
            FROM Slot
            WHERE range_id = $1
            ORDER BY slot
          `;
          const afterRangeSlots = await client.query(afterRangeQuery, [newBooking.rows[0].range_id]);

          // Check if any other slots were unexpectedly modified
          const unexpectedChanges = afterRangeSlots.rows.filter((afterSlot, index) => {
            const beforeSlot = beforeRangeSlots.rows[index];
            // Skip the slots we intentionally modified
            if (afterSlot.id === oldBooking.rows[0].slot_id || afterSlot.id === actualSlotIdRebook) {
              return false;
            }
            // Check if any values changed unexpectedly
            return afterSlot.remaining !== beforeSlot.remaining ||
                  afterSlot.noShowRemaining !== beforeSlot.noShowRemaining;
          });

          if (unexpectedChanges.length > 0) {
            logger.error(`Unexpected changes detected in slots: ${JSON.stringify(unexpectedChanges)}`);
            // If there are unexpected changes, we could rollback to the savepoint
            // await client.query('ROLLBACK TO SAVEPOINT slot_updates');

          } else {
            logger.info('No unexpected changes detected in other slots');
            await client.query('RELEASE SAVEPOINT slot_updates');
          }
        } catch (error) {
          logger.error(`Error updating slots: ${error}`);
          await client.query('ROLLBACK TO SAVEPOINT slot_updates');
          throw error;
        }


        try {
          let startTime: string = '', endTime: string = '';
          if (newBooking.rows[0]?.slot_details) {
            [startTime, endTime] = newBooking.rows[0].slot_details.split(' - ');
          }

          const startDateISO = DateTime.fromISO(`${newBooking.rows[0].range_date}T${startTime}`, {
            zone: 'Australia/Sydney',
          });

          const endDateISO = DateTime.fromISO(`${newBooking.rows[0].range_date}T${endTime}`, {
            zone: 'Australia/Sydney',
          });

          const currentTime = DateTime.now().setZone('Australia/Sydney');

          let queueTag = 'post-consult'; // Default

          if (currentTime < startDateISO.minus({ hours: 2 })) {
            queueTag = 'pre-consult';
          }

          // Get doctor name and initials for the booking
          let doctorName = '';
          let doctorInitials = '';
          try {
            let doctorId = assignedDoctorIdRebook;

            // If no assigned doctor from aggregated slot, get from range
            if (!doctorId) {
              const rangeQuery = `SELECT "doctorID" FROM Range WHERE id = $1`;
              const rangeResult = await client.query(rangeQuery, [actualRangeIdRebook]);
              if (rangeResult.rows.length > 0) {
                doctorId = rangeResult.rows[0].doctorID;
              }
            }

            // Get doctor name and initials using the doctor ID
            if (doctorId) {
              const doctorQuery = `SELECT username, initials FROM Dr WHERE id = $1`;
              const doctorResult = await client.query(doctorQuery, [doctorId]);
              if (doctorResult.rows.length > 0) {
                doctorName = doctorResult.rows[0].username;
                doctorInitials = doctorResult.rows[0].initials;
              }
            }
          } catch (doctorError) {
            logger.warn(`Could not retrieve doctor information for rebooking patient ${leadId}: ${doctorError}`);
          }

          const data = {
            data: [
              {
                Consult_Range_Start: startDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Consult_Range_End: endDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Consult_Date_Time: startDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Queue_Tags: queueTag,
                ...(doctorName && { Consulting_Doctor: doctorName }),
                ...(doctorInitials && { Consulting_Doctor_Initials_1: doctorInitials }),
              },
            ].map((objet) => {
              return req.params.flag && req.params.flag === 'sales'
                ? {
                    ...objet,
                    Booking_On_Call_Confirm_Link:
                      config.funnelZenithConfirmUrl +
                      '/confirm-on-call-booking?token=' +
                      UrlEncryptionHelper.encryptLeadId(leadId),
                  }
                : { ...objet };
            }),
          };
          const headers = await ZohoAuth.getHeaders();
          await axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });
          logger.info(`Successfully rebooked for patient ${leadId} between ${startDateISO} and ${endDateISO}${doctorName ? ` with doctor ${doctorName}${doctorInitials ? ` (${doctorInitials})` : ''}` : ''}`);
        } catch (e) {
          await client.query('ROLLBACK');
          const error = e as AxiosError;
          throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }

        await client.query('COMMIT');

        // Get the final state of the slots after all updates
        const finalOldSlotQuery = `SELECT id, slot, remaining, "noShowRemaining" FROM Slot WHERE id = $1`;
        const finalOldSlot = await client.query(finalOldSlotQuery, [oldBooking.rows[0].slot_id]);

        const finalNewSlotQuery = `SELECT id, slot, remaining, "noShowRemaining" FROM Slot WHERE id = $1`;
        const finalNewSlot = await client.query(finalNewSlotQuery, [actualSlotIdRebook]);

        if (finalNewSlot.rows.length > 0) {
          WebSocketManager.dispatch(TOPICS.NEW_BOOKING, [finalOldSlot.rows[0], finalNewSlot.rows[0]]);
          res.status(200).send([finalOldSlot.rows[0], finalNewSlot.rows[0]]);
          return;
        }
        res.status(200).send([finalOldSlot.rows[0]]);
      }
    } else {
      // Always check if patient is a no-show, regardless of rebooking flag
      // Get patient email to check if they are a no-show
      const patientQueryNew = `SELECT email FROM Patient WHERE "zohoID" = $1`;
      const patientResultNew = await client.query(patientQueryNew, [leadId]);
      const patientEmailNew = patientResultNew.rows.length > 0 ? patientResultNew.rows[0].email : null;

      // Check if patient is a no-show based on consultation history
      const noShowResultsNew = patientEmailNew ? await verifyNoShowPatient(patientEmailNew) : null;
      // Only consider the patient a no-show if the most recent consultation (first in the array) is a no-show
      const useNoShowPoolNew = noShowResultsNew && noShowResultsNew.length > 0 && noShowResultsNew[0].queueTag === 'no-show';


      // Handle random doctor assignment for aggregated slots
      let actualRangeId = body.range_id;
      let actualSlotId = body.id;
      let assignedDoctorId: string | null = null;

      // Check if this is an aggregated slot (starts with 'aggregated_')
      if (body.range_id && body.range_id.toString().startsWith('aggregated_')) {
        // This is an aggregated slot, we need to randomly assign a doctor
        const assignment = await assignRandomDoctor(client, body, useNoShowPoolNew || false);
        actualRangeId = assignment.range_id;
        actualSlotId = assignment.slot_id;
        assignedDoctorId = assignment.doctorID;

        logger.info(`Random doctor assignment for patient ${leadId}: range_id=${actualRangeId}, slot_id=${actualSlotId}, doctorID=${assignedDoctorId}`);
      }

      if (!req.params.flag || req.params.flag !== 'rebook') {
        // Only check availability if not rebooking
        // Select the appropriate availability pool based on patient history
        const checkSlotRemaining = useNoShowPoolNew
          ? `SELECT "noShowRemaining" as remaining FROM slot WHERE id = $1`
          : `SELECT remaining FROM slot WHERE id = $1`;

        const slotRemaining = await client.query(checkSlotRemaining, [actualSlotId]);

        if (
          !slotRemaining.rows.length ||
          slotRemaining.rows[0].remaining <= 0
        ) {
          logger.warn(`Failure to book for patient ${leadId} because slot ${actualSlotId} has no remaining availability`);
          throw new ApiError(httpStatus.BAD_REQUEST, 'Slot is full');
        }
      }

      // add flag to the booking: no-show
      // do not substract availability for such user

      const insertPatientBooking = `
      WITH upserted AS (
          INSERT INTO PatientSlot (patient_id, range_id, slot_id, "sameDayRebook", "queueType", "bookedByAdminId", "bookingType", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (patient_id, range_id)
          DO UPDATE SET
          slot_id = EXCLUDED.slot_id,
          "sameDayRebook" = EXCLUDED."sameDayRebook",
          "queueType" = EXCLUDED."queueType",
          "bookedByAdminId" = EXCLUDED."bookedByAdminId",
          "bookingType" = EXCLUDED."bookingType"
          RETURNING *
      )
      SELECT
          upserted.patient_id,
          upserted.range_id,
          upserted.slot_id,
          upserted."queueType",
          upserted."bookedByAdminId",
          upserted."bookingType",
          r.date AS range_date,
          s.slot AS slot_details
      FROM
          upserted
      JOIN
          Range r ON upserted.range_id = r.id
      JOIN
          Slot s ON upserted.slot_id = s.id;
    `;

      const flag = req.params.flag && req.params.flag === 'rebook' ? true : false;
      // Determine which queue type to use based on patient history
      const queueType = useNoShowPoolNew ? 'noShow' : 'regular';

      // Determine booking type and admin ID
      const isAdminBooking = req.params.flag === 'sales' || body.bookedByAdminId;
      const bookingType = isAdminBooking ? 'admin' : 'patient';
      const bookedByAdminId = body.bookedByAdminId || null;

      const values = [leadId, actualRangeId, actualSlotId, flag, queueType, bookedByAdminId, bookingType];
      const newBooking = await client.query(insertPatientBooking, values);



      // Determine which query to use for decrementing slot
      let decreaseNewSlot: string;
      if (useNoShowPoolNew) {

        decreaseNewSlot = `
          UPDATE Slot
          SET "noShowRemaining" = "noShowRemaining" - 1, "updatedAt"=CURRENT_TIMESTAMP
          WHERE id = $1 AND "noShowRemaining" > 0
          RETURNING id, slot, remaining, "noShowRemaining";
          `;
      } else {

        decreaseNewSlot = `
          UPDATE Slot
          SET remaining = remaining - 1, "updatedAt"=CURRENT_TIMESTAMP
          WHERE id = $1 AND remaining > 0
          RETURNING id, slot, remaining, "noShowRemaining";
          `;
      }

      // Execute the update with the actual assigned slot ID (not the aggregated one)
      const updatedSlot = await client.query(decreaseNewSlot, [actualSlotId]);




      // Send TO Zoho
      const headers = await ZohoAuth.getHeaders();
      try {
        const result = await axios.get(`${zohoLeadURL}/${leadId}`, { headers });
        
        if (!result?.data?.data) {
          await client.query('ROLLBACK');
          throw new ApiError(httpStatus.NOT_FOUND, 'Patient does not exist');
        }

        let startTime: string = '', endTime: string = '';
        if (newBooking.rows[0]?.slot_details) {
          [startTime, endTime] = newBooking.rows[0].slot_details.split(' - ');
        }

        const startDateISO = DateTime.fromISO(`${newBooking.rows[0].range_date}T${startTime}`, {
          zone: 'Australia/Sydney',
        });

        const endDateISO = DateTime.fromISO(`${newBooking.rows[0].range_date}T${endTime}`, {
          zone: 'Australia/Sydney',
        });

        const currentTime = DateTime.now().setZone('Australia/Sydney');

        let queueTag = 'post-consult'; // Default

        if (currentTime < startDateISO.minus({ hours: 2 })) {
          queueTag = 'pre-consult';
        }

        if (result.data?.data.length > 0) {
          // Get doctor name and initials for the booking
          let doctorName = '';
          let doctorInitials = '';
          try {
            let doctorId = assignedDoctorId;

            // If no assigned doctor from aggregated slot, get from range
            if (!doctorId) {
              const rangeQuery = `SELECT "doctorID" FROM Range WHERE id = $1`;
              const rangeResult = await client.query(rangeQuery, [actualRangeId]);
              if (rangeResult.rows.length > 0) {
                doctorId = rangeResult.rows[0].doctorID;
              }
            }

            // Get doctor name and initials using the doctor ID
            if (doctorId) {
              const doctorQuery = `SELECT username, initials FROM Dr WHERE id = $1`;
              const doctorResult = await client.query(doctorQuery, [doctorId]);
              if (doctorResult.rows.length > 0) {
                doctorName = doctorResult.rows[0].username;
                doctorInitials = doctorResult.rows[0].initials;
              }
            }
          } catch (doctorError) {
            logger.warn(`Could not retrieve doctor information for booking patient ${leadId}: ${doctorError}`);
          }

          const data = {
            data: [
              {
                Consult_Range_Start: startDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Consult_Range_End: endDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Consult_Date_Time: startDateISO.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
                Queue_Tags: queueTag,
                ...(doctorName && { Consulting_Doctor: doctorName }),
                ...(doctorInitials && { Consulting_Doctor_Initials_1: doctorInitials }),
              },
            ].map((objet) => {
              return req.params.flag && req.params.flag === 'sales'
                ? {
                    ...objet,
                    Booking_On_Call_Confirm_Link:
                      config.funnelZenithConfirmUrl +
                      '/confirm-on-call-booking?token=' +
                      UrlEncryptionHelper.encryptLeadId(leadId),
                  }
                : { ...objet };
            }),
          };
          await axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });
          logger.info(`Successfully Booked for patient ${leadId} between ${startDateISO} and ${endDateISO}${doctorName ? ` with doctor ${doctorName}${doctorInitials ? ` (${doctorInitials})` : ''}` : ''}`);
        }
      } catch (e) {
        const error = e as AxiosError;
        throw new ApiError(httpStatus.BAD_REQUEST, error.message);
      }
      if(req.params.flag ==='patient'){
        await client.query(
          `
      UPDATE PATIENT
      SET "lastCompletedForm"=$1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "zohoID"=$2`,
          ['booking', leadId],
        );
      }
      await client.query('COMMIT');

      if (updatedSlot.rows.length > 0) {
        WebSocketManager.dispatch(TOPICS.NEW_BOOKING, [updatedSlot.rows[0]]);
        res.status(200).send([updatedSlot.rows[0]]);
        return;
      }
      res.status(200).send([]);
    }
  };

  try {
    await queryHandler();
    return;
  } catch (e) {
    const error = e as AxiosError;
    await client.query('ROLLBACK');
    if (String(error.response?.status) === '401') {
      try {
        await ZohoAuth.forcedTokenRefresh();
        await queryHandler();
        return;
      } catch (e) {
        const error = e as AxiosError;
        throw new ApiError(httpStatus.BAD_REQUEST, error.message);
      }
    }
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

/**
 * Updates or clears the Last_Doctor_Message field in Zoho for a patient.
 * Expects: { zohoId: string, messageSnippet?: string, clear?: boolean }
 */
/**
 * Helper function to update Zoho contact with rejection notes
 * @param email - Patient email to find contact
 * @param rejectionNotes - Rejection reason to store in Messenger_Approval_Notes
 * @returns Promise<boolean> - true if successful, false if contact not found or update failed
 */
export const updateContactRejectionNotes = async (email: string, rejectionNotes: string): Promise<boolean> => {
  try {
    logger.info(`Updating rejection notes for contact with email: ${email}`);

    // First get the contact ID by email
    const contactResponse = await ZohoAuth.getZohoContactByEmail(email);

    if (!contactResponse || !contactResponse.data || contactResponse.data.length === 0) {
      logger.warn(`Contact not found for email: ${email}`);
      return false;
    }

    // Sort contacts by Created_Time descending (most recent first)
    const sortedContacts = contactResponse.data.sort((a: ZohoContact, b: ZohoContact) => {
      const aTime = new Date(a.Created_Time).getTime();
      const bTime = new Date(b.Created_Time).getTime();
      return bTime - aTime;
    });

    const contact = sortedContacts[0];
    const contactId = contact.id;

    // Prepare the update data
    const headers = await ZohoAuth.getHeaders();
    const data = {
      data: [
        {
          Messenger_Approval_Notes: rejectionNotes || '',
        },
      ],
    };

    // Update the contact
    const result = await axios.put(`${zohoContactURL}/${contactId}`, data, { headers });

    if (result?.data?.data?.[0]?.status === 'error') {
      logger.error(`Error updating contact ${contactId} with rejection notes:`, result?.data?.data?.[0]);
      return false;
    }

    return true;
  } catch (error) {
    const err = error as AxiosError;

    // Try again with token refresh if unauthorized
    if (err.response?.status === 401) {
      try {
        logger.info(`Zoho token expired, refreshing and retrying for email: ${email}`);
        await ZohoAuth.forcedTokenRefresh();
        const newHeaders = await ZohoAuth.getHeaders();

        // Re-get contact ID since we need to retry the whole process
        const contactResponse = await ZohoAuth.getZohoContactByEmail(email);

        if (!contactResponse || !contactResponse.data || contactResponse.data.length === 0) {
          logger.warn(`Contact not found for email: ${email} on retry`);
          return false;
        }

        const sortedContacts = contactResponse.data.sort((a: ZohoContact, b: ZohoContact) => {
          const aTime = new Date(a.Created_Time).getTime();
          const bTime = new Date(b.Created_Time).getTime();
          return bTime - aTime;
        });

        const contact = sortedContacts[0];
        const contactId = contact.id;

        const data = {
          data: [
            {
              Messenger_Approval_Notes: rejectionNotes || '',
            },
          ],
        };

        const refreshResult = await axios.put(`${zohoContactURL}/${contactId}`, data, { headers: newHeaders });

        if (refreshResult?.data?.data?.[0]?.status === 'error') {
          logger.error(`Error updating contact ${contactId} with rejection notes on retry:`, refreshResult?.data?.data?.[0]);
          return false;
        }

        return true;
      } catch (refreshError) {
        logger.error(`Error updating contact rejection notes on retry for email ${email}:`, refreshError);
        return false;
      }
    }

    logger.error(`Error updating contact rejection notes for email ${email}:`, err.message);
    return false;
  }
};

export const updateLastDoctorMessageField: RequestHandler = catchAll(async (req, res) => {
  const { zohoId, messageSnippet, clear } = req.body as {
    zohoId: string;
    messageSnippet?: string;
    clear?: boolean;
  };

  if (!zohoId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Missing zohoId');
  }

  const headers = await ZohoAuth.getHeaders();
  const data = {
    data: [
      {
        Messenger_Chat_from_Doctor: clear ? '' : (messageSnippet || '').slice(0, 50),
        Patient_Responded_to_Message: clear ? 'Yes' : 'No',
      },
    ],
  };

  try {
    const result = await axios.put(`${zohoContactURL}/${zohoId}`, data, { headers });

    if (result?.data?.data?.[0]?.status === 'error') {
      throw new ApiError(httpStatus.BAD_REQUEST, result?.data?.data?.[0]);
    }

    res.status(200).send({ success: true });
  } catch (error) {
    const err = error as AxiosError;

    // Try again with token refresh if unauthorized
    if (err.response?.status === 401) {
      try {

        await ZohoAuth.forcedTokenRefresh();
        const newHeaders = await ZohoAuth.getHeaders();
        const refreshResult = await axios.put(`${zohoContactURL}/${zohoId}`, data, { headers: newHeaders });

        if (refreshResult?.data?.data?.[0]?.status === 'error') {

          throw new ApiError(httpStatus.BAD_REQUEST, refreshResult?.data?.data?.[0]);
        }

        res.status(200).send({ success: true });
        return;
      } catch (refreshError) {
        const rErr = refreshError as AxiosError;
        throw new ApiError(httpStatus.BAD_REQUEST, rErr.message);
      }
    }

    throw new ApiError(httpStatus.BAD_REQUEST, err.message);
  }
});


export const getContactDetailsByZohoId: RequestHandler = catchAll(async (req, res) => {
  const zohoId = req.params.zohoId;

  if (!zohoId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Zoho Contact ID is required');
  }

  try {
    // Get headers with auth token
    const headers = await ZohoAuth.getHeaders();

    // Try to fetch the contact from Zoho CRM
    const response = await axios.get(`${zohoContactURL}/${zohoId}`, { headers });

    if (response.data && response.data.data && response.data.data.length > 0) {
      const contactData = response.data.data[0];

      // Extract treatment plans for both concentrations
      const treatment22 = {
        dailyDose: contactData.Dose_Per_Day_22_1 || null,
        maxDailyDose: contactData.Maximum_Doses_per_Day_22_1 || null,
        totalQuantity: contactData.Total_Qty_22_1 || null,
        pouchCount: contactData.Pouch_Count_22_1 || null,
        strength: "22%"
      };

      const treatment29 = {
        dailyDose: contactData.Dose_Per_Day_29_1 || null,
        maxDailyDose: contactData.Maximum_Doses_per_Day_29_1 || null,
        totalQuantity: contactData.Total_Qty_29_1 || null,
        pouchCount: contactData.Pouch_Count_29_1 || null,
        strength: "29%"
      };

      // Extract strain advice (collect all non-null advice)
      const strainAdvice = Array.from({ length: 18 }, (_, i) => i + 1)
        .map(num => ({
          [`advice${num}`]: contactData[`StrainAdvice${num}`]
        }))
        .filter(advice => Object.values(advice)[0] !== null)
        .reduce((acc, curr) => ({ ...acc, ...curr }), {});

      // Extract side effects
      const sideEffects = {
        effect1: contactData.SubSideEffect1 || null,
        effect2: contactData.SubSideEffect2 || null,
        effect3: contactData.SubSideEffect3 || null
      };

      // Extract previous treatments
      const previousTreatments = Array.from({ length: 8 }, (_, i) => i + 1)
        .map(num => contactData[`OtherTreatment${num}`])
        .filter(treatment => treatment !== null);

      // Common treatment data
      const commonTreatmentData = {
        dispensingInterval: contactData.Dispensing_Interval_Period_1 || null,
        numberOfRepeats: contactData.Number_of_Repeats_1 || null,
        supplyDate: contactData.Supply_Date_1 || null,
        supplyExpiration: contactData.Supply_Expiration || null,
        doctorNotes: contactData.Doctor_Notes || null
      };

      // Return the relevant contact details and treatment data

      // Return the relevant contact details, focusing on email
      res.status(200).send({
        success: true,
        email: contactData.Email || null,
        firstName: contactData.First_Name || null,
        lastName: contactData.Last_Name || null,
        phone: contactData.Phone || null,
        mobile: contactData.Mobile || null,
        id: contactData.id || zohoId,
        treatmentPlan: {
          ...commonTreatmentData,
          treatments: {
            t22: treatment22.dailyDose ? treatment22 : null,
            t29: treatment29.dailyDose ? treatment29 : null
          },
          strainAdvice,
          sideEffects,
          previousTreatments
        }
      });
    } else {
      // No contact found with that ID
      res.status(404).send({
        success: false,
        message: 'Contact not found in Zoho CRM',
        email: null
      });
    }
  } catch (e) {
    console.error('Error fetching Zoho contact details:', e);
    if (e instanceof ApiError) {
      throw e;
    }
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      'Failed to retrieve contact details from Zoho CRM'
    );
  }
});



export const getContactIdByEmail: RequestHandler = catchAll(async (req, res) => {
  const email = req.query.email as string;
  logger.info(`Getting contact ID for email: ${email}`);
  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Email is required');
  }
  try {
    const response = await ZohoAuth.getZohoContactByEmail(email);

    if (response && response.data && response.data.length > 0) {
      // Sort contacts by Created_Time descending (most recent first)
      const sortedContacts = response.data.sort((a: ZohoContact, b: ZohoContact) => {
        const aTime = new Date(a.Created_Time).getTime();
        const bTime = new Date(b.Created_Time).getTime();
        return bTime - aTime;
      });
      const contact = sortedContacts[0];
      res.status(200).send({ success: true, id: contact.id, email: contact.Email || null });
    } else {
      res.status(404).send({ success: false, message: 'Contact not found', id: null });
    }
  } catch (e) {
    console.error('Error fetching Zoho contact by email:', e);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve contact ID from Zoho CRM');
  }
});

export const getLeadByEmail: RequestHandler = catchAll(async (req, res) => {
  const email = req.query.email as string;
  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Email is required');
  }
  try {
    const response = await ZohoAuth.getZohoLeadByEmail(email);


    if (response && response.data && response.data.length > 0) {
      // Sort contacts by Created_Time descending (most recent first)
      const sortedLeads = response.data.sort((a: ZohoLead, b: ZohoLead) => {
        const aTime = new Date(a.Created_Time).getTime();
        const bTime = new Date(b.Created_Time).getTime();
        return bTime - aTime;
      });
      const lead = sortedLeads[0];

      res.status(200).send({ success: true, id: lead.id, email: lead.Email || null, member_status: lead.Member_Status  });
    } else {
      res.status(404).send({ success: false, message: 'lead not found', id: null });
    }
  } catch (e) {
    console.error('Error fetching Zoho contact by email:', e);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve contact ID from Zoho CRM');
  }
});

/**
 * Gets treatment plan data from a Zoho contact by email
 * Returns data mapped to ZohoTreatmentPlan type
 */
export const getTreatmentPlanByEmail: RequestHandler = catchAll(async (req, res) => {
  const email = req.query.email as string;


  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Email is required');
  }

  try {
    // First get the contact by email
    const response = await ZohoAuth.getZohoContactByEmail(email);

    if (!response || !response.data || response.data.length === 0) {
      res.status(404).send({
        success: false,
        message: 'Contact not found',
        treatmentPlan: null
      });
      return;
    }

    // Sort contacts by Created_Time descending (most recent first)
    const sortedContacts = response.data.sort((a: ZohoContact, b: ZohoContact) => {
      const aTime = new Date(a.Created_Time).getTime();
      const bTime = new Date(b.Created_Time).getTime();
      return bTime - aTime;
    });

    const contactData = sortedContacts[0];

    // Helper function to get next repeat date from Zoho or return N/A message
    const getNextRepeatDate = (zohoField: string): string => {
      return zohoField || 'N/A - initial order not placed';
    };

    // Map Zoho contact data to ZohoTreatmentPlan structure
    const treatmentPlan: ZohoTreatmentPlan = {
      consultingDoctor: contactData.Consulting_Doctor || contactData.Full_Name || '',
      treatmentPlanStartDate: contactData.Supply_Date_1 || '',
      treatmentPlanEndDate: contactData.Supply_Expiration || '',
      thcContent: contactData.Strength_Concentration || `22%: ${contactData.Total_Qty_22_1 || '0'}g, 29%: ${contactData.Total_Qty_29_1 || '0'}g`,
      totalAllowance: {
        thc22: contactData.Total_Qty_22_1 || '0',
        thc29: contactData.Total_Qty_29_1 || '0'
      },
      totalAllowanceUsed: {
        // These fields don't exist in current Zoho structure, return N/A
        thc22: 'N/A - initial order not placed',
        thc29: 'N/A - initial order not placed'
      },
      repeatAllowance: {
        thc22: contactData.Dose_Per_Day_22_1 || '0',
        thc29: contactData.Dose_Per_Day_29_1 || '0'
      },
      numberOfRepeats: parseInt(contactData.Number_of_Repeats_1 || '0'),
      repeatsRemaining: {
        // These fields don't exist in current Zoho structure, return N/A
        thc22: parseInt(contactData.Number_of_Repeats_1) || 0, // Use actual repeats or 0 if not set
        thc29: parseInt(contactData.Number_of_Repeats_1) || 0  // Use actual repeats or 0 if not set
      },
      nextRepeatDate: {
        // Use actual Zoho next repeat fields or return N/A message
        thc22: getNextRepeatDate(contactData.Next_Repeat_1),
        thc29: getNextRepeatDate(contactData.Next_Repeat_1)
      },
      supplyRemainingForRepeat: {
        // This would need to be calculated from actual order data, return N/A for now
        thc22: 'N/A - initial order not placed',
        thc29: 'N/A - initial order not placed'
      }
    };

    // Additional useful information from Zoho contact
    const additionalInfo = {
      memberStatus: contactData.Member_Status,
      memberID: contactData.Member_ID1,
      doctorNotes: contactData.Doctor_Notes,
      mentalHealthSupportingDocumentation: contactData.Mental_Health_Supporting_Documentation,
      dispensingInterval: contactData.Dispensing_Interval_Period_1,
      maximumDosesPerDay: {
        thc22: contactData.Maximum_Doses_per_Day_22_1,
        thc29: contactData.Maximum_Doses_per_Day_29_1
      },
      pouchCount: {
        thc22: contactData.Pouch_Count_22_1,
        thc29: contactData.Pouch_Count_29_1
      },
      lastModified: contactData.Modified_Time,
      createdTime: contactData.Created_Time
    };

    res.status(200).send({
      success: true,
      email: contactData.Email || email,
      contactId: contactData.id,
      fullName: contactData.Full_Name,
      treatmentPlan,
      additionalInfo
    });

  } catch (e) {
    console.error('Error fetching treatment plan by email:', e);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve treatment plan from Zoho CRM');
  }
});

/**
 * Get patient's current and upcoming bookings
 * Expects: { email: string } (Patient email)
 */
export const getPatientBookings: RequestHandler = catchAll(async (req, res) => {
  const email = req.params.email;

  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient email is required');

  }

  const client = await db.connect();

  try {
    // First, get the Zoho ID from email (PatientSlot uses Zoho ID as patient_id)
    const patientQuery = `
      SELECT "zohoID"
      FROM patient
      WHERE email = $1
    `;

    const patientResult = await client.query(patientQuery, [email]);

    if (patientResult.rows.length === 0) {
      res.status(404).send({
        success: false,
        message: 'Patient not found with this email',
        bookings: [],
        totalBookings: 0
      });
      return;
    }

    const zohoId = patientResult.rows[0].zohoID;

    if (!zohoId) {
      res.status(404).send({
        success: false,
        message: 'Patient does not have a Zoho ID associated',
        bookings: [],
        totalBookings: 0
      });
      return;
    }

    // Query for patient bookings - only show today and future bookings based on Sydney timezone
    const bookingsQuery = `
      SELECT
        ps.patient_id,
        ps.range_id,
        ps.slot_id,
        ps."queueType",
        ps."bookedByAdminId",
        ps."bookingType",
        ps."createdAt",
        ps."updatedAt",
        r.date AS booking_date,
        r.day AS booking_day,
        r.start AS range_start,
        r.end AS range_end,
        r.status AS range_status,
        s.slot AS time_slot,
        s.remaining,
        s."noShowRemaining",
        dr.name AS booked_by_admin_name,
        dr.email AS booked_by_admin_email,
        -- Get doctor information from the range
        range_dr.name AS assigned_doctor_name,
        range_dr.email AS assigned_doctor_email
      FROM patientslot ps
      JOIN range r ON ps.range_id = r.id
      JOIN slot s ON ps.slot_id = s.id
      LEFT JOIN dr dr ON ps."bookedByAdminId" = dr."accessID"
      LEFT JOIN dr range_dr ON r."doctorID" = range_dr.id
      WHERE ps.patient_id = $1
        AND r.date::DATE >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND r.status = 'active'
      ORDER BY r.date ASC, s.slot ASC
    `;

    const result = await client.query(bookingsQuery, [zohoId]);

    // Transform the data to be more user-friendly
    const bookings = result.rows.map(row => {
      // Get current date/time in Australia/Sydney timezone
      const sydneyNow = new Date().toLocaleString("en-US", {timeZone: "Australia/Sydney"});
      const sydneyCurrentDate = new Date(sydneyNow);

      // Parse the booking date (stored as text like "2025-06-10")
      const bookingDate = new Date(row.booking_date);

      // Set Sydney current date to start of day for comparison
      sydneyCurrentDate.setHours(0, 0, 0, 0);

      return {
        bookingId: `${row.range_id}-${row.slot_id}`,
        patientId: row.patient_id,
        date: row.booking_date,
        day: row.booking_day,
        timeSlot: row.time_slot,
        queueType: row.queueType,
        bookingType: row.bookingType,
        assignedDoctor: {
          name: row.assigned_doctor_name,
          email: row.assigned_doctor_email
        },
        bookedBy: row.booked_by_admin_name ? {
          name: row.booked_by_admin_name,
          email: row.booked_by_admin_email,
          type: 'admin'
        } : {
          type: 'patient'
        },
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        canCancel: bookingDate >= sydneyCurrentDate // Can only cancel today or future bookings (Sydney time)
      };
    });

    res.status(200).send({
      success: true,
      email,
      leadId: zohoId, // Return Zoho ID as patientId for consistency with other endpoints
      bookings,
      totalBookings: bookings.length
    });

  } catch (error) {
    logger.error('Error fetching patient bookings:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve patient bookings');
  } finally {
    client.release();
  }
});

export const createPatientFromZohoContact: RequestHandler = catchAll(async (req, res) => {
  const zohoId = req.params.zohoId;

  if (!zohoId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Zoho Contact ID is required');
  }

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Get headers with auth token
    const headers = await ZohoAuth.getHeaders();

    // Fetch the contact from Zoho CRM
    const response = await axios.get(`${zohoContactURL}/${zohoId}`, { headers });

    if (!response?.data?.data || response.data.data.length === 0) {
      await client.query('ROLLBACK');
      throw new ApiError(httpStatus.NOT_FOUND, 'Contact not found in Zoho CRM');
    }

    const contactData = response.data.data[0];

    // Validate required fields
    if (!contactData.Email) {
      await client.query('ROLLBACK');
      throw new ApiError(httpStatus.BAD_REQUEST, 'Contact must have an email address');
    }

    if (!contactData.Member_ID1) {
      await client.query('ROLLBACK');
      throw new ApiError(httpStatus.BAD_REQUEST, 'Contact must have a Member ID');
    }

    // Prepare patient data
    const patientData = {
      fullName: `${contactData.First_Name || ''} ${contactData.Last_Name || ''}`.trim() || 'Unknown',
      email: contactData.Email.toLowerCase(),
      returningPatient: false, // Default to false for new patients created from contacts
      patientID: contactData.Member_ID1,
      zohoID: zohoId,
      mobile: contactData.Mobile || contactData.Phone || null,
      state: contactData.Mailing_State || null,
      dob: contactData.Date_of_Birth || null
    };

    // Insert or update patient in database
    const query = `
      INSERT INTO Patient ("fullName", email, "returningPatient", "patientID", "zohoID", mobile, state, dob, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT ("patientID")
      DO UPDATE SET
          "fullName" = EXCLUDED."fullName",
          email = EXCLUDED.email,
          "returningPatient" = EXCLUDED."returningPatient",
          "zohoID" = EXCLUDED."zohoID",
          mobile = EXCLUDED.mobile,
          state = EXCLUDED.state,
          dob = EXCLUDED.dob,
          "updatedAt" = CURRENT_TIMESTAMP
      RETURNING *;
    `;

    const values = [
      patientData.fullName,
      patientData.email,
      patientData.returningPatient,
      patientData.patientID,
      patientData.zohoID,
      patientData.mobile,
      patientData.state,
      patientData.dob
    ];

    const result = await client.query(query, values);
    const createdPatient = result.rows[0];

    // Extract and store questionnaire data if available
    const questionnaireData = {
      Condition: contactData.Condition || null,
      What_condition_or_symptom_are_you_having_issues_wi:
        contactData.What_condition_or_symptom_are_you_having_issues_wi || null,
      Are_you_planning_to_have_children_in_the_near_futu:
        contactData.Are_you_planning_to_have_children_in_the_near_futu || null,
      Do_you_suffer_from_any_cardiovascular_diseases_in:
        contactData.Do_you_suffer_from_any_cardiovascular_diseases_in || null,
      Do_you_suffer_from_psychosis_bipolar_disorder_or:
        contactData.Do_you_suffer_from_psychosis_bipolar_disorder_or || null,
      Do_you_have_an_addiction_to_any_psychoactive_subst:
        contactData.Do_you_have_an_addiction_to_any_psychoactive_subst || null,
      What_was_your_gender_at_birth: contactData.What_was_your_gender_at_birth || null,
      Please_add_the_first_medication_treatment_or_ther:
        contactData.Please_add_the_first_medication_treatment_or_ther || null,
      Please_add_the_second_medication_treatment_or_the:
        contactData.Please_add_the_second_medication_treatment_or_the || null,
      Have_you_used_Alternative_Medicines_before_whethe:
        contactData.Have_you_used_Alternative_Medicines_before_whethe || null,
      Knowing_the_alternative_management_options_do_you:
        contactData.Knowing_the_alternative_management_options_do_you || null
    };

    // Insert questionnaire data if any exists
    const questionnairePlaceholders: string[] = [];
    const questionnaireValues: unknown[] = [];
    let qOffset = 0;

    Object.entries(questionnaireData).forEach(([question, answer]) => {
      if (answer && answer !== 'No Answer') {
        questionnairePlaceholders.push(
          `($${qOffset + 1}, $${qOffset + 2}, $${qOffset + 3}, $${qOffset + 4}, $${qOffset + 5}, $${qOffset + 6}, $${qOffset + 7}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        );

        questionnaireValues.push(
          patientData.patientID, // patientID
          zohoId, // zohoID
          patientData.email, // email
          question, // question text
          answer, // answer text
          'questionnaire', // type
          zohoId, // Unique questionnaireID
        );

        qOffset += 7;
      }
    });

    let questionnaireInserted = false;
    if (questionnairePlaceholders.length > 0) {
      const questionnaireQuery = `
        INSERT INTO Questionnaire (
          "patientID", "zohoID", "email", "question", "answers", "type", "questionnaireID", "createdAt", "updatedAt"
        ) VALUES ${questionnairePlaceholders.join(', ')}
        ON CONFLICT (email, "patientID", question)
        DO UPDATE SET
          "answers" = EXCLUDED."answers",
          "updatedAt" = CURRENT_TIMESTAMP;
      `;

      await client.query(questionnaireQuery, questionnaireValues);
      questionnaireInserted = true;
      logger.info(`Inserted ${questionnairePlaceholders.length} questionnaire entries for patient: ${patientData.patientID}`);
    }

    await client.query('COMMIT');

    logger.info(`Successfully created/updated patient from Zoho contact: ${patientData.patientID} (${patientData.email})`);

    res.status(200).send({
      success: true,
      message: 'Patient created successfully from Zoho contact',
      patient: {
        id: createdPatient.id,
        fullName: createdPatient.fullName,
        email: createdPatient.email,
        patientID: createdPatient.patientID,
        zohoID: createdPatient.zohoID,
        mobile: createdPatient.mobile,
        state: createdPatient.state,
        dob: createdPatient.dob,
        returningPatient: createdPatient.returningPatient,
        createdAt: createdPatient.createdAt,
        updatedAt: createdPatient.updatedAt
      },
      questionnaire: {
        inserted: questionnaireInserted,
        entriesCount: questionnairePlaceholders.length
      }
    });

  } catch (e) {
    await client.query('ROLLBACK');
    logger.error('Error creating patient from Zoho contact:', e);

    if (e instanceof ApiError) {
      throw e;
    }

    // Handle specific database errors
    if (e instanceof Error && e.message.includes('duplicate key')) {
      throw new ApiError(httpStatus.CONFLICT, 'Patient with this email or ID already exists');
    }

    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      'Failed to create patient from Zoho contact'
    );
  } finally {
    client.release();
  }
});
