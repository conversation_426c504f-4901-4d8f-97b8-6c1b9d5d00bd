import { catchAll } from '../../utils/catchAll';
import { <PERSON>questHand<PERSON> } from 'express';
import { StreamChat, Channel, DefaultGenerics } from 'stream-chat';
import httpStatus from 'http-status';
import { ApiError } from '../../utils/ApiError';
import config from '../../config';
import { PatientTreatmentPlan } from '../../types';
import { ZohoAuth, zohoLeadURL, zohoContactURL } from '../../helpers/zoho';
import axios from 'axios';
import { logger } from '../../config/logger';
//import { WebSocketManager } from '../../helpers/webSocketManager';
import { AxiosError } from 'axios';
//import { TOPICS } from '../../helpers/topics';
import { getFormatedZohoDate, getStrength } from '../../utils/misc';
import { db } from '../../utils/db';
import moderationService from '../../services/moderation.service';

import { ApiResponse } from '../../helpers/response';

// Initialize Stream Chat server client using config
const apiKey = config.streamChat.apiKey;
const apiSecret = config.streamChat.apiSecret;

const streamClient = StreamChat.getInstance(apiKey, apiSecret);

// Message and Event Types

interface ChannelData {
  name?: string;
  member_count?: number;
  [key: string]: unknown;
}

interface ChatMessage {
  text: string;
  custom_type?: string;
  patient_id?: string;
  timestamp?: string;
  sender_id?: string;
}

interface ChatEvent {
  type: string;
  patient_id?: string;
  timestamp?: string;
  sender_id?: string;
}

// GetStream Chat Attachment Interface
// interface StreamChatAttachment {
//   type?: string;
//   title?: string;
//   title_link?: string;
//   image_url?: string;
//   asset_url?: string;
//   file_size?: number;
//   mime_type?: string;
// }

// Processed attachment data for storage
// interface AttachmentData {
//   type?: string;
//   title?: string;
//   title_link?: string;
//   image_url?: string;
//   asset_url?: string;
//   file_size?: number;
//   mime_type?: string;
// }

/**
 * Format user IDs to be compatible with Stream Chat (a-z, 0-9, @, _, -)
 * @param id - Original ID to format
 * @param prefix - Optional prefix (e.g., 'p_' or 'd_')
 * @returns Formatted ID safe for Stream Chat
 */
const formatStreamChatId = (id: string, prefix?: string): string => {
  // Max length to allow for IDs, accounting for prefix and potential concatenation in channel IDs
  const MAX_ID_LENGTH = 20;

  // Sanitize the ID: replace invalid chars with underscore and convert to lowercase
  const sanitized = id.toString().replace(/[^a-z0-9@_-]/gi, '_').toLowerCase();

  // Truncate to ensure the ID isn't too long
  const truncated = sanitized.substring(0, MAX_ID_LENGTH);

  // Add prefix if provided
  return prefix ? `${prefix}_${truncated}` : truncated;
};

/**
 * Generate a Stream Chat token for a user
 * @route POST /chat/token
 */
export const generateToken: RequestHandler = catchAll(async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'User ID is required');
  }

  try {
    //console.log('Generating token for user:', userId);

    // Generate token with expiration (e.g., 24 hours)
    const expirationTime = Math.floor(Date.now() / 1000) + 60 * 60 * 24;
    const token = streamClient.createToken(userId, expirationTime);

    console.log('Token generated successfully, expires at:', new Date(expirationTime * 1000).toISOString());

    res.status(200).send({
      token,
      expires_at: expirationTime
    });
  } catch (error) {
    console.error('Error generating Stream Chat token:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to generate token');
  }
});

/**
 * Refresh a Stream Chat token for a user
 * @route POST /chat/refresh-token
 */
export const refreshToken: RequestHandler = catchAll(async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'User ID is required');
  }

  try {
    // console.log('Refreshing token for user:', userId);

    // Generate a fresh token with a new expiration time
    const expirationTime = Math.floor(Date.now() / 1000) + 60 * 60 * 24;
    const token = streamClient.createToken(userId, expirationTime);

    res.status(200).send({
      token,
      expires_at: expirationTime
    });
  } catch (error) {
    console.error('Error refreshing Stream Chat token:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to refresh token');
  }
});

/**
 * Create or update a channel for a patient-doctor interaction
 * @route POST /chat/channel
 */
export const createChannel: RequestHandler = catchAll(async (req, res) => {
  try {


    // Check for required fields
    const { userId1, userId2, channelName, userType1 = 'doctor', userType2 = 'patient' } = req.body;
    if (!userId1 || !userId2) {

      throw new ApiError(httpStatus.BAD_REQUEST, 'userId1 and userId2 are required');
    }

    // Format IDs to be Stream Chat compatible (only a-z, 0-9, @, _, -)
    const formattedId1 = formatStreamChatId(userId1, userType1 === 'doctor' ? 'd' : 'p');
    const formattedId2 = formatStreamChatId(userId2, userType2 === 'doctor' ? 'd' : 'p');



    // Create a Stream Chat server client
    const chatClient = StreamChat.getInstance(
      config.streamChat.apiKey,
      config.streamChat.apiSecret
    );

    // Generate a unique channel ID based on user IDs - keep it under 64 chars
    const channelId = `${formattedId1}_${formattedId2}`;

    // Try to create or update users first
    try {
      // Extract patient name from channelName if available
      // The channelName is typically in the format "Chat with Patient Name" or just "Patient Name"
      let patientName = channelName || '';
      if (patientName.startsWith('Chat with ')) {
        patientName = patientName.substring('Chat with '.length);
      }

      // Helper function to get doctor display name
      const getDoctorDisplayName = async (userId: string): Promise<string> => {
        try {
          // For the default "harvest" doctor ID, fetch from database
          if (userId === 'harvest') {
            const client = await db.connect();
            try {
              // Query for any doctor with role='doctor' - in practice there should be one active doctor
              const query = `SELECT name, username FROM Dr WHERE role = 'doctor' ORDER BY "updatedAt" DESC LIMIT 1`;
              const result = await client.query(query);

              if (result.rows.length > 0) {
                const doctor = result.rows[0];
                return doctor.username || doctor.name || 'Doctor';
              }
            } finally {
              client.release();
            }
          }
          return 'Doctor';
        } catch (error) {
          console.error('Error fetching doctor name:', error);
          return 'Doctor';
        }
      };

      // Get doctor names for both users if they are doctors
      const user1Name = userType1 === 'doctor' ? await getDoctorDisplayName(userId1) : patientName || `Patient ${userId1}`;
      const user2Name = userType2 === 'doctor' ? await getDoctorDisplayName(userId2) : patientName || `Patient ${userId2}`;

      await chatClient.upsertUsers([
        {
          id: formattedId1,
          role: 'user',
          name: user1Name,
        },
        {
          id: formattedId2,
          role: 'user',
          name: user2Name,
        }
      ]);
    } catch (userError) {
      console.error('Error creating or updating users:', userError);
      // Continue with channel creation even if user creation fails
    }

    // Create a new channel

    const channel = chatClient.channel('messaging', channelId, {
      members: [formattedId1, formattedId2],
      created_by_id: formattedId1,
      name: channelName // Set the channel name
    });

    // Initialize the channel
    const response = await channel.create();


    res.status(httpStatus.CREATED).json({
      channelId,
      formattedId1,
      formattedId2,
      response
    });
  } catch (error) {
    console.error('Detailed error creating Stream Chat channel:', error);

    // Return a more helpful error response
    if (error instanceof Error) {
      res.status(500).json({
        message: 'Failed to create channel',
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
      return;
    }

    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to create channel');
  }
});

/**
 * Safely serialize a Stream Chat channel to avoid circular references
 * @param channel - Stream Chat channel object
 * @returns Serializable channel data
 */
const serializeChannel = (channel: Channel<DefaultGenerics>) => {
  // Extract only the necessary properties we need from the channel
  const lastMessage = channel.state.messages[channel.state.messages.length - 1];

  return {
    id: channel.id,
    type: channel.type,
    cid: channel.cid,
    created_at: new Date().toISOString(), // Fallback since we can't access internal date
    updated_at: new Date().toISOString(), // Fallback since we can't access internal date
    last_message_at: channel.state.last_message_at || new Date().toISOString(),
    member_count: (channel.data as ChannelData)?.member_count || 0,
    data: channel.data || {},
    config: channel.getConfig() || {},
    name: (channel.data as ChannelData)?.name,
    members: Object.keys(channel.state?.members || {}).map(key => ({
      user_id: channel.state?.members?.[key].user_id,
      user: {
        id: channel.state?.members?.[key].user?.id,
        name: channel.state?.members?.[key].user?.name,
        role: channel.state?.members?.[key].user?.role,
      }
    })),
    // Include last message if available
    last_message: lastMessage ? {
      id: lastMessage.id,
      text: lastMessage.text,
      user_id: lastMessage.user?.id,
      created_at: lastMessage.created_at,
      updated_at: lastMessage.updated_at,
    } : null,
  };
};

/**
 * Get all channels for a user
 * @route GET /chat/channels/:userId
 */
export const getUserChannels: RequestHandler = catchAll(async (req, res) => {
  const { userId } = req.params;

  if (!userId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'User ID is required');
  }

  try {


    // Query channels where the user is a member
    const filter = { type: 'messaging', members: { $in: [userId] } };
    // Sort descending by last_message_at (-1 means descending)
    const sort = { last_message_at: -1 as const };

    const channels = await streamClient.queryChannels(filter, sort);


    // Serialize channels to avoid circular references
    const serializedChannels = channels.map(serializeChannel);

    res.status(200).send({ channels: serializedChannels });
  } catch (error) {
    console.error('Error fetching user channels:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch channels');
  }
});

/**
 * Add a member to a channel
 * @route POST /chat/channel/:channelId/members
 */
export const addChannelMember: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;
  const { userId } = req.body;

  if (!channelId || !userId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID and User ID are required');
  }

  try {
    // Get the channel
    const channel = streamClient.channel('messaging', channelId);

    // Add the member
    const response = await channel.addMembers([userId]);

    res.status(200).send({
      channelId,
      members: response.members
    });
  } catch (error) {
    console.error('Error adding member to channel:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to add member to channel');
  }
});

/**
 * Remove a member from a channel
 * @route DELETE /chat/channel/:channelId/members/:userId
 */
export const removeChannelMember: RequestHandler = catchAll(async (req, res) => {
  const { channelId, userId } = req.params;

  if (!channelId || !userId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID and User ID are required');
  }

  try {
    // Get the channel
    const channel = streamClient.channel('messaging', channelId);

    // Remove the member
    const response = await channel.removeMembers([userId]);

    res.status(200).send({
      channelId,
      members: response.members
    });
  } catch (error) {
    console.error('Error removing member from channel:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to remove member from channel');
  }
});

/**
 * Send a message to a channel
 * @route POST /chat/channel/:channelId/message
 */
export const sendChannelMessage: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;
  const { message } = req.body as { message: ChatMessage };

  if (!channelId || !message) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID and message are required');
  }

  try {
    // Get the channel
    const channel = streamClient.channel('messaging', channelId);

    // Create the message object with custom data
    const messageData = {
      text: message.text,
      // If there's custom data, add it with custom_ prefix
      ...(message.custom_type && { custom_type: message.custom_type }),
      ...(message.patient_id && { patient_id: message.patient_id }),
      ...(message.timestamp && { timestamp: message.timestamp })
    };

    // Extract the sender ID from the channel ID if not provided
    // Channel IDs are typically in the format "d_harvest_p_14622"
    const channelParts = channelId.split('_');
    // Default to the doctor ID (first part of the channel ID) if no sender is specified
    const senderId = message.sender_id || (channelParts[0] === 'd' ? channelId.split('p_')[0].slice(0, -1) : `d_harvest`);

    // Send the message with the required user_id
    const response = await channel.sendMessage({
      text: messageData.text,
      user_id: senderId, // Required for server-side auth
      custom: {
        type: message.custom_type,
        patient_id: message.patient_id,
        timestamp: message.timestamp
      }
    });

    res.status(200).send({
      channelId,
      message: response.message
    });
  } catch (error) {
    console.error('Error sending message to channel:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to send message to channel');
  }
});

/**
 * Send a hidden message to a channel (for signaling treatment plan submission)
 * @route POST /chat/channel/:channelId/event
 */
export const sendChannelEvent: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;
  const { event } = req.body as { event: ChatEvent };

  if (!channelId || !event) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID and event data are required');
  }

  try {
    // Get the channel
    const channel = streamClient.channel('messaging', channelId);

    // Extract the sender ID from the channel ID
    // Channel IDs are typically in the format "d_harvest_p_14622"
    const channelParts = channelId.split('_');
    // Default to the doctor ID (first part of the channel ID)
    const senderId = event.sender_id || (channelParts[0] === 'd' ? channelId.split('p_')[0].slice(0, -1) : `d_harvest`);

    // Instead of sending a custom event, send a hidden message with a special type
    // This is more reliable across different Stream Chat versions
    const response = await channel.sendMessage({
      text: "", // Empty text so nothing shows in the UI
      user_id: senderId,
      custom_type: "hidden.treatment.plan.submitted", // Special type for client to filter
      custom: {
        type: event.type,
        patient_id: event.patient_id,
        timestamp: event.timestamp
      }
    });

    res.status(200).send({
      channelId,
      message: response.message
    });
  } catch (error) {
    console.error('Error sending hidden message to channel:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to send event to channel');
  }
});

/**
 * Get treatment plans for a patient by patientID
 * @route GET /chat/patient/:patientId/treatment-plans
 */
export const getPatientTreatmentPlans: RequestHandler = catchAll(async (req, res) => {
  const { patientId } = req.params;

  if (!patientId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  const client = await db.connect();
  try {
    // First get the patient's email
    const patientResult = await client.query(
      `SELECT email FROM Patient WHERE "patientID" = $1`,
      [patientId]
    );

    if (patientResult.rows.length === 0) {
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
      return;
    }

    const patientEmail = patientResult.rows[0].email;

    // Query treatment plans using the patient's email
    const query = `
      SELECT * FROM TreatmentPlan
      WHERE email = $1 OR "patientID" = $2
      ORDER BY "createdAt" DESC
    `;

    const result = await client.query(query, [patientEmail, patientId]);

    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows, true));
  } catch (error) {
    console.error('Error fetching patient treatment plans:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch treatment plans');
  } finally {
    client.release();
  }
});

export const postChatTreatmentPlan: RequestHandler = catchAll(async (req, res) => {
  const body = req.body as PatientTreatmentPlan;
  const patientID = body.patient?.patientID;
  const zohoID = body.zohoID; // Using the prescription lead's zohoID

  const client = await db.connect();

  await client.query('BEGIN');
  try {

    // Fetch patient data to get email if not provided
    let patientEmail = body.patient?.email;

    if (!patientEmail) {
      const patientResult = await client.query(
        `SELECT email FROM Patient WHERE "patientID" = $1`,
        [patientID]
      );

      if (patientResult.rows.length > 0) {
        patientEmail = patientResult.rows[0].email;
      }
    }

    // Update patient status
    await client.query(
      `
      UPDATE Patient
      SET "drLocked" = $1, locked = $2, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $3
      `,
      [null, false, patientID],
    );

    // // Update consultation status
    // await client.query(
    //   `
    //   UPDATE Consultation
    //   SET "meetingOngoing" = $1, completed = $2, "updatedAt" = CURRENT_TIMESTAMP
    //   WHERE "patientID" = $3
    //   `,
    //   [false, true, patientID],
    // );


    // Preserve doctor notes exactly as received, only use default if undefined
    const drNotes = body.drNotes !== undefined ? body.drNotes : '';

    // Ensure we always have a doctor name
    const drName = body.drName || '';

    // Save treatment plan with patient email in the email column
    const treatmentPlanQuery = `
    INSERT INTO TreatmentPlan (
      "patientID",
      "drId",
      "consultationId",
      "updatedAt",
      outcome,
      "drNotes",
      "diagnosis",
      date,
      "drName",
      "strengthAndConcentration22",
      "dosePerDay22",
      "maxDose22",
      "totalQuantity22",
      "numberOfRepeat22",
      "supplyInterval22",
      "strengthAndConcentration29",
      "dosePerDay29",
      "maxDose29",
      "totalQuantity29",
      "numberOfRepeat29",
      "supplyInterval29",
      email,
      "mentalHealthSupprtingDocument",
      "idVerified",
      "type",
      "source",
      "createdAt"
    )
    VALUES (
      $1, $2, $3, CURRENT_TIMESTAMP, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
      $14, $15, $16, $17, $18, $19, $20, $21, $22, 'treatmentplan', 'messenger', CURRENT_TIMESTAMP
    )
    `;

    await client.query(treatmentPlanQuery, [
      body.patient?.patientID,
      body.drId,
      body.patient?.consultation?.id,
      body.outcome,
      drNotes, // Use just the doctor notes without email data
      body.diagnosis, // Add diagnosis field
      body.date,
      drName, // Use the doctor name with fallback
      body[22] ? '22%' : undefined,
      body[22]?.dosePerDay,
      body[22]?.maxDosePerDay,
      body[22]?.totalQuantity,
      body[22]?.numberOfRepeat,
      body[22]?.supplyInterval,
      body[29] ? '29%' : undefined,
      body[29]?.dosePerDay,
      body[29]?.maxDosePerDay,
      body[29]?.totalQuantity,
      body[29]?.numberOfRepeat,
      body[29]?.supplyInterval,
      patientEmail, // Use the patient's actual email address
      body.mentalHealthSupportingDocumentation,
      body.idVerified,
    ]);

    // Prepare Zoho data
    const headers = await ZohoAuth.getHeaders();
    const strainAdvice = [body?.email?.checkedSativa, body.email?.checkedIndica, body.email?.checkedHybrid]
      .flat()
      .filter((a) => a !== undefined)
      .reduce(
        (strainAdvice, value, index) => {
          strainAdvice[`strainAdvice${index + 1}`] = value;
          return strainAdvice;
        },
        {} as { [key: string]: string },
      );

    let otherTreatment: {
      [key: string]: string;
    } = {};

    if (body?.email?.otherTreatment) {
      otherTreatment = Object.keys(body.email.otherTreatment).reduce(
        (otherTreatment, key, index) => {
          if (body.email?.otherTreatment?.[key]) {
            otherTreatment[`OtherTreatment${index + 1}`] = body.email?.otherTreatment?.[key];
          }
          return otherTreatment;
        },
        {} as { [key: string]: string },
      );
    }

    // Update Zoho prescription lead
    const data = {
      data: [
        {
          Dr_Approve_Date_Time: getFormatedZohoDate(),
          Strength_Concentration: getStrength(body),
          Dr_Trigger: body.outcome,
          Mental_Health_Supporting_Documentation: body.mentalHealthSupportingDocumentation,
          ID_Verified: body.idVerified,
          Consulting_Doctor: body.drName,
          Prescription_Date_1: getFormatedZohoDate().split('T')[0],
          Specified_Dose: body[22]?.dosePerDay,
          Maximum_Doses_per_Day: body[22]?.maxDosePerDay,
          Total_Qty_22_1: body[22]?.totalQuantity,
          Number_of_Repeats_22: body[22]?.numberOfRepeat,
          Dose_Per_Day_29: body[29]?.dosePerDay,
          Maximum_Doses_per_Day_29: body[29]?.maxDosePerDay,
          Number_of_Repeats_29: body[22]?.numberOfRepeat ? body[22]?.numberOfRepeat : body[29]?.numberOfRepeat,
          Total_Qty_29_1: body[29]?.totalQuantity,
          Dispensing_Interval_Period_1: body[22]?.supplyInterval ? body[22]?.supplyInterval : body[29]?.supplyInterval,
          Doctor_Notes: body.drNotes !== undefined ? body.drNotes : '',
          Dr_AHPRA_Number: body.drAphraNumber,
          Introduction1: body?.email?.introMessage?.intro,
          Introduction2: body?.email?.introMessage?.conclusion,
          SideEffect1: body?.email?.listTitle?.title1,
          SideEffect2: body?.email?.listTitle?.title2,
          SideEffect3: body?.email?.listTitle?.title3,
          SubSideEffect1: body?.email?.listItemText?.item1,
          SubSideEffect2: body?.email?.listItemText?.item2,
          SubSideEffect3: body?.email?.listItemText?.item3,
          ...strainAdvice,
          ...otherTreatment,
        },
      ],
    };

    if (!zohoID) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Zoho ID is required');
    }

    // Update Zoho lead with treatment plan and email content
    const result = await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });
    if (result?.data?.data?.[0]) {
      if (result?.data?.data?.[0].status === 'error') {
        await client.query('ROLLBACK');
        throw new ApiError(httpStatus.BAD_REQUEST, result?.data?.data?.[0]);
      }

      if (result?.data?.data?.[0].status === 'success') {
        logger.info(`Successfully updated Patient :: ${zohoID} in Zoho`);
      }
    }

    await client.query('COMMIT');

    // Notify websocket clients
    // WebSocketManager.dispatch(TOPICS.REMOVE_ID, {
    //   patientID,
    // });

    res.status(200).send({
      patientID,
      message: 'Treatment plan submitted and email sent successfully',
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;

    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});



// Batch endpoint to get latest treatment plans for multiple patients
export const getLatestTreatmentPlansForPatients: RequestHandler = catchAll(async (req, res) => {
  const { patientIds } = req.body;
  if (!Array.isArray(patientIds) || patientIds.length === 0) {
    res.status(400).json({ error: 'patientIds must be a non-empty array' });
    return;
  }

  // Define interface for the treatment plan record to avoid using 'any'
  interface TreatmentPlanHistoryRecord {
    id: string;
    fullName: string;
    patientID: string;
    consultationId: string;
    createdAt: Date;
    updatedAt: Date;
    drName: string;
    drId?: string;
    outcome: string;
    treatmentPlan: {
      dispensingInterval: string | null;
      numberOfRepeats: string | null;
      doctorNotes: string | null;
      treatments: {
        t22: {
          dailyDose: string;
          maxDailyDose: string;
          totalQuantity: string;
          pouchCount: string | null;
          strength: string;
        } | null;
        t29: {
          dailyDose: string;
          maxDailyDose: string;
          totalQuantity: string;
          pouchCount: string | null;
          strength: string;
        } | null;
      };
    };
    date: string;
    dosePerDay22: string;
    maxDosePerDay22: string;
    strengthAndConcentration22: string;
    totalQuantity22: string;
    numberOfRepeat22: string;
    supplyInterval22: string;
    dosePerDay29: string;
    maxDosePerDay29: string;
    strengthAndConcentration29: string;
    totalQuantity29: string;
    numberOfRepeat29: string;
    supplyInterval29: string;
    drNotes: string;
    mentalHealthSupprtingDocument: string;
    type: string;
    totalRows: string;
  }

  const client = await db.connect();
  try {
    // Query latest treatment plan for each patientId
    const placeholders = patientIds.map((_, i) => `$${i + 1}`).join(',');
    const query = `
      SELECT DISTINCT ON ("patientID") tp.*, p."fullName"
      FROM TreatmentPlan tp
      LEFT JOIN Patient p ON tp."patientID" = p."patientID"
      WHERE tp."patientID" IN (${placeholders})
      ORDER BY tp."patientID", tp."createdAt" DESC
    `;
    const result = await client.query(query, patientIds);

    // Map patientId to latest plan with transformed data
    const planMap: Record<string, TreatmentPlanHistoryRecord | null> = {};

    // Initialize with transformed DB plans
    for (const patientId of patientIds) {
      const dbPlan = result.rows.find(plan => plan.patientID === patientId);

      if (dbPlan) {
        // Transform to TreatmentPlanHistory format
        planMap[patientId] = {
          id: dbPlan.id,
          fullName: dbPlan.fullName || '',
          patientID: dbPlan.patientID,
          consultationId: dbPlan.consultationId || '',
          createdAt: dbPlan.createdAt,
          updatedAt: dbPlan.updatedAt,
          drName: dbPlan.drName || '',
          drId: dbPlan.drId || undefined,
          outcome: dbPlan.outcome || '',
          treatmentPlan: {
            dispensingInterval: dbPlan.supplyInterval22 || dbPlan.supplyInterval29 || null,
            numberOfRepeats: dbPlan.numberOfRepeat22 || dbPlan.numberOfRepeat29 || null,
            doctorNotes: dbPlan.drNotes || null,
            treatments: {
              t22: dbPlan.strengthAndConcentration22 ? {
                dailyDose: String(dbPlan.dosePerDay22 || ''),
                maxDailyDose: String(dbPlan.maxDose22 || ''),
                totalQuantity: String(dbPlan.totalQuantity22 || ''),
                pouchCount: null,
                strength: dbPlan.strengthAndConcentration22
              } : null,
              t29: dbPlan.strengthAndConcentration29 ? {
                dailyDose: String(dbPlan.dosePerDay29 || ''),
                maxDailyDose: String(dbPlan.maxDose29 || ''),
                totalQuantity: String(dbPlan.totalQuantity29 || ''),
                pouchCount: null,
                strength: dbPlan.strengthAndConcentration29
              } : null
            }
          },
          date: dbPlan.date || '',
          dosePerDay22: String(dbPlan.dosePerDay22 || ''),
          maxDosePerDay22: String(dbPlan.maxDose22 || ''),
          strengthAndConcentration22: dbPlan.strengthAndConcentration22 || '',
          totalQuantity22: String(dbPlan.totalQuantity22 || ''),
          numberOfRepeat22: String(dbPlan.numberOfRepeat22 || ''),
          supplyInterval22: String(dbPlan.supplyInterval22 || ''),
          dosePerDay29: String(dbPlan.dosePerDay29 || ''),
          maxDosePerDay29: String(dbPlan.maxDose29 || ''),
          strengthAndConcentration29: dbPlan.strengthAndConcentration29 || '',
          totalQuantity29: String(dbPlan.totalQuantity29 || ''),
          numberOfRepeat29: String(dbPlan.numberOfRepeat29 || ''),
          supplyInterval29: String(dbPlan.supplyInterval29 || ''),
          drNotes: dbPlan.drNotes || '',
          mentalHealthSupprtingDocument: dbPlan.mentalHealthSupprtingDocument || '',
          type: dbPlan.type || '',
          totalRows: '1' // Since this is just one row per patient
        };
      } else {
        planMap[patientId] = null;
      }
    }

    res.status(200).json(planMap);
    return;
  } catch (error) {
    console.error('Error fetching batch treatment plans:', error);
    res.status(500).json({ error: 'Failed to fetch batch treatment plans' });
    return;
  } finally {
    client.release();
  }
});

/**
 * Get the latest treatment plan for a patient by patientID
 * @route GET /chat/patient/:patientId/treatment-plan-latest
 */
export const getLatestPatientTreatmentPlan: RequestHandler = catchAll(async (req, res) => {
  const { patientId } = req.params;

  if (!patientId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  const client = await db.connect();
  try {
    // Get patient's email, zohoId and fullName in a single query
    const patientResult = await client.query(
      `SELECT email, "zohoID", "fullName" FROM Patient WHERE "patientID" = $1`,
      [patientId]
    );

    if (patientResult.rows.length === 0) {
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      return;
    }

    const { email: patientEmail, zohoID, fullName } = patientResult.rows[0];

    // Query the latest treatment plan using the patient's email or patientID
    const query = `
      SELECT * FROM TreatmentPlan
      WHERE email = $1 OR "patientID" = $2
      ORDER BY "createdAt" DESC
      LIMIT 1
    `;

    const result = await client.query(query, [patientEmail, patientId]);
    const dbPlan = result.rows[0] || null;

    if (!dbPlan) {
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      return;
    }

    // Define treatment types with consistent structure
    type TreatmentData = {
      dailyDose: string;
      maxDailyDose: string;
      totalQuantity: string;
      pouchCount: string | null;
      strength: string;
    };

    // Define treatment plan structure
    type TreatmentPlanData = {
      dispensingInterval: string | null;
      numberOfRepeats: string | null;
      doctorNotes: string | null;
      supplyDate?: string | null;
      supplyExpiration?: string | null;
      treatments: {
        t22: TreatmentData | null;
        t29: TreatmentData | null;
      };
      strainAdvice?: Record<string, string>;
      sideEffects?: {
        effect1: string | null;
        effect2: string | null;
        effect3: string | null;
      };
      previousTreatments?: string[];
    };

    // Initial transformation to TreatmentPlanHistory format
    const treatmentPlanData = {
      id: dbPlan.id,
      fullName: fullName || '',
      patientID: dbPlan.patientID,
      consultationId: dbPlan.consultationId || '',
      createdAt: dbPlan.createdAt,
      updatedAt: dbPlan.updatedAt,
      drName: dbPlan.drName || '',
      drId: dbPlan.drId || undefined,
      outcome: dbPlan.outcome || '',
      treatmentPlan: {
        dispensingInterval: dbPlan.supplyInterval22 || dbPlan.supplyInterval29 || null,
        numberOfRepeats: dbPlan.numberOfRepeat22 || dbPlan.numberOfRepeat29 || null,
        doctorNotes: dbPlan.drNotes || null,
        treatments: {
          t22: dbPlan.strengthAndConcentration22 ? {
            dailyDose: String(dbPlan.dosePerDay22 || ''),
            maxDailyDose: String(dbPlan.maxDose22 || ''),
            totalQuantity: String(dbPlan.totalQuantity22 || ''),
            pouchCount: null,
            strength: dbPlan.strengthAndConcentration22
          } as TreatmentData : null,
          t29: dbPlan.strengthAndConcentration29 ? {
            dailyDose: String(dbPlan.dosePerDay29 || ''),
            maxDailyDose: String(dbPlan.maxDose29 || ''),
            totalQuantity: String(dbPlan.totalQuantity29 || ''),
            pouchCount: null,
            strength: dbPlan.strengthAndConcentration29
          } as TreatmentData : null
        }
      } as TreatmentPlanData,
      date: dbPlan.date || '',
      dosePerDay22: String(dbPlan.dosePerDay22 || ''),
      maxDosePerDay22: String(dbPlan.maxDose22 || ''),
      strengthAndConcentration22: dbPlan.strengthAndConcentration22 || '',
      totalQuantity22: String(dbPlan.totalQuantity22 || ''),
      numberOfRepeat22: String(dbPlan.numberOfRepeat22 || ''),
      supplyInterval22: String(dbPlan.supplyInterval22 || ''),
      dosePerDay29: String(dbPlan.dosePerDay29 || ''),
      maxDosePerDay29: String(dbPlan.maxDose29 || ''),
      strengthAndConcentration29: dbPlan.strengthAndConcentration29 || '',
      totalQuantity29: String(dbPlan.totalQuantity29 || ''),
      numberOfRepeat29: String(dbPlan.numberOfRepeat29 || ''),
      supplyInterval29: String(dbPlan.supplyInterval29 || ''),
      drNotes: dbPlan.drNotes || '',
      mentalHealthSupprtingDocument: dbPlan.mentalHealthSupprtingDocument || '',
      type: dbPlan.type || '',
      totalRows: '1'
    };

    // If we have a zohoID, fetch the Zoho data
    if (zohoID) {
      try {
        const headers = await ZohoAuth.getHeaders();
        const zohoResponse = await axios.get(`${zohoContactURL}/${zohoID}`, { headers });

        if (zohoResponse.data?.data?.[0]) {
          const contactData = zohoResponse.data.data[0];

          // Extract treatment plans for both concentrations
          const treatment22: TreatmentData | null = contactData.Dose_Per_Day_22_1 ? {
            dailyDose: String(contactData.Dose_Per_Day_22_1 || ''),
            maxDailyDose: String(contactData.Maximum_Doses_per_Day_22_1 || ''),
            totalQuantity: String(contactData.Total_Qty_22_1 || ''),
            pouchCount: contactData.Pouch_Count_22_1 ? String(contactData.Pouch_Count_22_1) : null,
            strength: "22%"
          } : null;

          const treatment29: TreatmentData | null = contactData.Dose_Per_Day_29_1 ? {
            dailyDose: String(contactData.Dose_Per_Day_29_1 || ''),
            maxDailyDose: String(contactData.Maximum_Doses_per_Day_29_1 || ''),
            totalQuantity: String(contactData.Total_Qty_29_1 || ''),
            pouchCount: contactData.Pouch_Count_29_1 ? String(contactData.Pouch_Count_29_1) : null,
            strength: "29%"
          } : null;

          // Extract strain advice
          const strainAdvice = Array.from({ length: 18 }, (_, i) => i + 1)
            .map(num => ({
              [`advice${num}`]: contactData[`StrainAdvice${num}`]
            }))
            .filter(advice => Object.values(advice)[0] !== null)
            .reduce((acc, curr) => ({ ...acc, ...curr }), {});

          // Extract side effects
          const sideEffects = {
            effect1: contactData.SubSideEffect1 || null,
            effect2: contactData.SubSideEffect2 || null,
            effect3: contactData.SubSideEffect3 || null
          };

          // Extract previous treatments
          const previousTreatments = Array.from({ length: 8 }, (_, i) => i + 1)
            .map(num => contactData[`OtherTreatment${num}`])
            .filter(treatment => treatment !== null);

          // Common treatment data
          const commonTreatmentData = {
            dispensingInterval: contactData.Dispensing_Interval_Period_1 || null,
            numberOfRepeats: contactData.Number_of_Repeats_1 || null,
            supplyDate: contactData.Supply_Date_1 || null,
            supplyExpiration: contactData.Supply_Expiration || null,
            doctorNotes: contactData.Doctor_Notes || null
          };

          // Update treatmentPlan with Zoho data
          treatmentPlanData.treatmentPlan = {
            ...treatmentPlanData.treatmentPlan,
            ...commonTreatmentData,
            treatments: {
              t22: treatment22 || treatmentPlanData.treatmentPlan.treatments?.t22 || null,
              t29: treatment29 || treatmentPlanData.treatmentPlan.treatments?.t29 || null
            },
            strainAdvice,
            sideEffects,
            previousTreatments
          };
        }
      } catch (error) {
        console.error('Error fetching Zoho data:', error);
        // Continue with DB data only
      }
    }

    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', treatmentPlanData, true));
  } catch (error) {
    console.error('Error fetching latest patient treatment plan:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch latest treatment plan');
  } finally {
    client.release();
  }
});

/**
 * Get the latest treatment plan for a patient, including doctor info
 * @route GET /chat/patient/:patientId/latest-treatment-plan-with-doctor
 */
export const getLatestTreatmentPlanWithDoctor: RequestHandler = catchAll(async (req, res) => {
  const { patientId } = req.params;

  if (!patientId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  const client = await db.connect();
  try {
    // First get the patient's email
    const patientResult = await client.query(
      `SELECT email FROM Patient WHERE "patientID" = $1`,
      [patientId]
    );

    if (patientResult.rows.length === 0) {
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', { treatmentPlan: null, doctor: null }, true));
      return;
    }

    const patientEmail = patientResult.rows[0].email;

    // Query the latest treatment plan and join doctor info
    const query = `
      SELECT tp.*, d.name as doctor_name, d.username as doctor_username, d.email as doctor_email, d."accessID" as doctor_accessID
      FROM TreatmentPlan tp
      LEFT JOIN Dr d ON tp."drId" = d."accessID"
      WHERE tp.email = $1 OR tp."patientID" = $2
      ORDER BY tp."createdAt" DESC
      LIMIT 1
    `;

    const result = await client.query(query, [patientEmail, patientId]);
    const row = result.rows[0] || null;

    let treatmentPlan = null;
    let doctor: { name?: string; username?: string; email?: string; accessID?: string } | null = null;
    if (row) {
      const {
        doctor_name,
        doctor_username,
        doctor_email,
        doctor_accessID,
        ...plan
      } = row;
      treatmentPlan = plan;
      if (doctor_name || doctor_username || doctor_email || doctor_accessID) {
        doctor = {
          name: doctor_name,
          username: doctor_username,
          email: doctor_email,
          accessID: doctor_accessID
        };
      }
    }

    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', { treatmentPlan, doctor: doctor ?? null }, true));
  } catch (error) {
    console.error('Error fetching latest patient treatment plan with doctor:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch latest treatment plan with doctor');
  } finally {
    client.release();
  }
});

/**
 * Check conversation visibility for chat interface
 * @route POST /chat/check-conversation-visibility
 */
export const checkConversationVisibility: RequestHandler = catchAll(async (req, res) => {
  const { channelIds } = req.body;

  if (!channelIds || !Array.isArray(channelIds) || channelIds.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel IDs array is required');
  }

  const client = await db.connect();

  try {
    // Create placeholders for the IN clause
    const placeholders = channelIds.map((_, index) => `$${index + 1}`).join(',');

    const query = `
      SELECT "channelId", "moderationStatus", "isVisible"
      FROM chat_conversation_moderation
      WHERE "channelId" IN (${placeholders})
    `;

    const result = await client.query(query, channelIds);

    // Create a map of channelId to visibility status
    const visibilityMap: Record<string, boolean> = {};

    result.rows.forEach(row => {
      // Only show approved conversations
      visibilityMap[row.channelId] = row.moderationStatus === 'approved' && row.isVisible;
    });

    // For conversations not in moderation table, they should NOT be visible to doctors
    // All patient-initiated conversations must go through moderation first
    channelIds.forEach(channelId => {
      if (visibilityMap[channelId] === undefined) {
        visibilityMap[channelId] = false; // Hide unmoderated conversations
      }
    });

    res.status(200).json({
      success: true,
      data: visibilityMap
    });
  } finally {
    client.release();
  }
});

/**
 * Check message visibility for chat interface (legacy method)
 * @route POST /chat/check-message-visibility
 */
export const checkMessageVisibility: RequestHandler = catchAll(async (req, res) => {
  const { messageIds } = req.body;

  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Message IDs array is required');
  }

  // For the new system, all messages in approved conversations are visible
  // This is a simplified implementation for backward compatibility
  const visibilityMap: Record<string, boolean> = {};

  messageIds.forEach(messageId => {
    // Assume all messages are visible for now
    // In a full implementation, we'd need to map messages to channels
    visibilityMap[messageId] = true;
  });

  res.status(200).json({
    success: true,
    data: visibilityMap
  });
});

/**
 * GetStream webhook handler for message moderation
 * @route POST /chat/webhook
 */
export const handleStreamWebhook: RequestHandler = catchAll(async (req, res) => {
  const { type, message, channel } = req.body;

  // Only process new messages
  if (type !== 'message.new' || !message || !channel) {
    res.status(200).json({ success: true });
    return;
  }

  // Handle doctor messages - just acknowledge them (notifications are handled directly by frontend)
  if (message.user?.id?.startsWith('d_')) {
    res.status(200).json({ success: true });
    return;
  }

  // Only moderate patient messages (user IDs starting with 'p_')
  if (!message.user?.id?.startsWith('p_')) {
    res.status(200).json({ success: true });
    return;
  }

  // Skip system messages, but allow messages with attachments even if no text
  if (message.type === 'system') {
    res.status(200).json({ success: true });
    return;
  }

  // Check if message has content (text or attachments)
  const hasText = message.text && message.text.trim() !== '';
  const hasAttachments = message.attachments && message.attachments.length > 0;

  if (!hasText && !hasAttachments) {
    res.status(200).json({ success: true });
    return;
  }

  try {
    // Extract patient ID from user ID (remove 'p_' prefix)
    const patientId = message.user.id.replace('p_', '');

    // Get patient information from database
    const client = await db.connect();
    let patientName = message.user.name || 'Unknown Patient';

    try {
      const patientQuery = `
        SELECT "fullName"
        FROM patient
        WHERE "patientID" = $1
      `;
      const patientResult = await client.query(patientQuery, [patientId]);

      if (patientResult.rows.length > 0) {
        const patient = patientResult.rows[0];
        patientName = patient.fullName || patientName;
      }
    } finally {
      client.release();
    }

    // Process attachment data if present
    //let attachmentData: AttachmentData[] | null = null;
    //let attachmentTypes: string[] = [];
    //const attachmentCount = message.attachments?.length || 0;

    // if (hasAttachments) {
    //   //const attachments = message.attachments as StreamChatAttachment[];
    //   //attachmentTypes = attachments.map((att) => att.type || 'unknown');
    //   // attachmentData = attachments.map((att): AttachmentData => ({
    //   //   type: att.type,
    //   //   title: att.title,
    //   //   title_link: att.title_link,
    //   //   image_url: att.image_url,
    //   //   asset_url: att.asset_url,
    //   //   file_size: att.file_size,
    //   //   mime_type: att.mime_type
    //   // }));
    // }

    // Store conversation for moderation (new system)
    const moderationResult = await moderationService.storeConversationForModeration({
      channelId: channel.id,
      patientId: patientId,
      treatmentPlanId: undefined, // Will be linked automatically in the service
      patientName: patientName,
      messageText: message.text || '',
      hasAttachments
    });

    // Send Slack notification for manual review (only for new conversations)
    if (moderationResult.isNewConversation) {
      // Get the conversation details for notification
      const client = await db.connect();
      try {
        const conversationQuery = `
          SELECT * FROM conversation_moderation_summary
          WHERE "channelId" = $1
        `;
        const conversationResult = await client.query(conversationQuery, [channel.id]);

        if (conversationResult.rows.length > 0) {
          const conversation = conversationResult.rows[0];
          await moderationService.sendConversationModerationNotification(
            conversation,
            message.text || ''
          );
        }
      } finally {
        client.release();
      }
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error processing webhook message:', error);
    // Don't fail the webhook - just log the error
    res.status(200).json({ success: true });
  }
});

// Chat Notification Service Functions

/**
 * Set chat notification flag for a patient
 */
const setChatNotification = async (patientEmail: string, doctorId: string, value: boolean = true): Promise<void> => {
  const client = await db.connect();
  try {
    const query = `
      INSERT INTO chat_notifications (patient_email, chat_notification, last_doctor_message_at, doctor_id, updated_at)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      ON CONFLICT (patient_email)
      DO UPDATE SET
        chat_notification = $2,
        last_doctor_message_at = CASE WHEN $2 = true THEN $3 ELSE chat_notifications.last_doctor_message_at END,
        doctor_id = CASE WHEN $2 = true THEN $4 ELSE chat_notifications.doctor_id END,
        updated_at = CURRENT_TIMESTAMP
    `;

    const timestamp = value ? new Date() : null;
    await client.query(query, [patientEmail, value, timestamp, doctorId]);

    logger.info(`Chat notification ${value ? 'set' : 'cleared'} for patient ${patientEmail} by doctor ${doctorId}`);
  } finally {
    client.release();
  }
};

/**
 * Get chat notification state for a patient
 */
export const getChatNotification: RequestHandler = catchAll(async (req, res) => {
  const { email } = req.params;

  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient email is required');
  }

  const client = await db.connect();
  try {
    const query = `
      SELECT * FROM chat_notifications
      WHERE patient_email = $1
    `;

    const result = await client.query(query, [email]);

    if (result.rows.length === 0) {
      res.status(200).json({
        success: true,
        message: 'No notification record found',
        data: {
          patientEmail: email,
          chatNotification: false,
          lastDoctorMessageAt: null,
          doctorId: null
        }
      });
      return;
    }

    const notification = result.rows[0];
    res.status(200).json({
      success: true,
      message: 'Chat notification retrieved successfully',
      data: {
        patientEmail: notification.patient_email,
        chatNotification: notification.chat_notification,
        lastDoctorMessageAt: notification.last_doctor_message_at,
        doctorId: notification.doctor_id
      }
    });

  } finally {
    client.release();
  }
});

/**
 * Get all patients with active chat notifications
 */
export const getAllChatNotifications: RequestHandler = catchAll(async (req, res) => {
  const { active } = req.query;

  const client = await db.connect();
  try {
    let query = `
      SELECT * FROM chat_notifications
    `;

    const params: (string | boolean)[] = [];

    if (active === 'true') {
      query += ` WHERE chat_notification = true`;
    }

    query += ` ORDER BY last_doctor_message_at DESC`;

    const result = await client.query(query, params);

    const notifications = result.rows.map(row => ({
      patientEmail: row.patient_email,
      chatNotification: row.chat_notification,
      lastDoctorMessageAt: row.last_doctor_message_at,
      doctorId: row.doctor_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    res.status(200).json({
      success: true,
      message: 'Chat notifications retrieved successfully',
      data: notifications
    });

  } finally {
    client.release();
  }
});

/**
 * Mark chat notification as read (clear notification)
 */
export const markChatNotificationRead: RequestHandler = catchAll(async (req, res) => {
  const { email } = req.params;

  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient email is required');
  }

  await setChatNotification(email, '', false);

  res.status(200).json({
    success: true,
    message: 'Chat notification marked as read',
    data: { patientEmail: email, chatNotification: false }
  });
});



/**
 * Set chat notification when doctor sends message (API endpoint)
 */
export const setChatNotificationForMessage: RequestHandler = catchAll(async (req, res) => {
  const { patientEmail, doctorId } = req.body;

  if (!patientEmail || !doctorId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient email and Doctor ID are required');
  }

  await setChatNotification(patientEmail, doctorId, true);

  res.status(200).json({
    success: true,
    message: 'Chat notification set successfully',
    data: { patientEmail, doctorId, chatNotification: true }
  });
});

/**
 * Clear chat notification when doctor approves/rejects request (internal function)
 */
export const clearChatNotificationForRequest = async (patientEmail: string): Promise<void> => {
  try {
    await setChatNotification(patientEmail, '', false);
  } catch (error) {
    logger.error(`Failed to clear chat notification for patient ${patientEmail}:`, error);
    // Don't throw error to avoid breaking request functionality
  }
};
