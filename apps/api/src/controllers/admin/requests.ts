import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';
import { ApiResponse } from '../../helpers/response';
import { db } from '../../utils/db';

/**
 * Get pending requests for admin view (reuses existing logic)
 */
export const getAdminPendingRequests: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    const query = `
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.patient_id,
        tiq.email,
        tiq.questionnaire_data,
        tiq.total_score,
        tiq.max_score,
        tiq.is_eligible,
        tiq.status,
        tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob
      FROM thc_increase_questionnaire tiq
      LEFT JOIN patient p ON tiq.patient_id = p."patientID"
      WHERE tiq.status IN ('pending', 'submitted') AND tiq.is_eligible = true

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.patient_id,
        etq.email,
        etq.questionnaire_data,
        etq.total_score,
        etq.max_score,
        etq.is_eligible,
        etq.status,
        etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob
      FROM extend_tp_questionnaire etq
      LEFT JOIN patient p ON etq.patient_id = p."patientID"
      WHERE etq.status IN ('pending', 'submitted') AND etq.is_eligible = true

      ORDER BY created_at DESC
    `;

    const result = await client.query(query);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', { requests: result.rows }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get processed requests (approved/rejected) for admin view
 */
export const getAdminProcessedRequests: RequestHandler = catchAll(async (req, res) => {
  const { status, type, limit = '50', page = '1' } = req.query;
  const client = await db.connect();

  try {
    let statusFilter = '';
    if (status === 'approved' || status === 'rejected') {
      statusFilter = `AND tiq.status = '${status}'`;
    } else {
      statusFilter = `AND tiq.status IN ('approved', 'rejected')`;
    }

    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = '';

    if (type === 'thc_increase') {
      // Only THC increase requests
      query = `
        SELECT
          'thc_increase' as type,
          tiq.id,
          tiq.patient_id,
          tiq.email,
          tiq.total_score,
          tiq.max_score,
          tiq.status,
          tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(tiq.approved_at, tiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(tiq.approved_by, tiq.reviewed_by) as reviewed_by,
          tiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name
        FROM thc_increase_questionnaire tiq
        LEFT JOIN patient p ON tiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(tiq.approved_by, tiq.reviewed_by) = d."accessID"
        WHERE tiq.status IN ('approved', 'rejected') ${statusFilter}
        ORDER BY COALESCE(tiq.approved_at, tiq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else if (type === 'extend_tp') {
      // Only extend TP requests
      query = `
        SELECT
          'extend_tp' as type,
          etq.id,
          etq.patient_id,
          etq.email,
          etq.total_score,
          etq.max_score,
          etq.status,
          etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(etq.approved_at, etq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(etq.approved_by, etq.reviewed_by) as reviewed_by,
          etq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name
        FROM extend_tp_questionnaire etq
        LEFT JOIN patient p ON etq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(etq.approved_by, etq.reviewed_by) = d."accessID"
        WHERE etq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'etq.status')}
        ORDER BY COALESCE(etq.approved_at, etq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else {
      // Both types (default)
      query = `
        SELECT
          'thc_increase' as type,
          tiq.id,
          tiq.patient_id,
          tiq.email,
          tiq.total_score,
          tiq.max_score,
          tiq.status,
          tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(tiq.approved_at, tiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(tiq.approved_by, tiq.reviewed_by) as reviewed_by,
          tiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name
        FROM thc_increase_questionnaire tiq
        LEFT JOIN patient p ON tiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(tiq.approved_by, tiq.reviewed_by) = d."accessID"
        WHERE tiq.status IN ('approved', 'rejected') ${statusFilter}

        UNION ALL

        SELECT
          'extend_tp' as type,
          etq.id,
          etq.patient_id,
          etq.email,
          etq.total_score,
          etq.max_score,
          etq.status,
          etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(etq.approved_at, etq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(etq.approved_by, etq.reviewed_by) as reviewed_by,
          etq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name
        FROM extend_tp_questionnaire etq
        LEFT JOIN patient p ON etq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(etq.approved_by, etq.reviewed_by) = d."accessID"
        WHERE etq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'etq.status')}

        ORDER BY reviewed_at DESC
        LIMIT $1 OFFSET $2
      `;
    }

    const result = await client.query(query, [limit, offset]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', { requests: result.rows }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get request statistics for admin dashboard
 */
export const getAdminRequestStats: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    const query = `
      SELECT 
        COUNT(*) FILTER (WHERE status IN ('pending', 'submitted') AND is_eligible = true) as pending,
        COUNT(*) FILTER (WHERE status = 'approved') as approved,
        COUNT(*) FILTER (WHERE status = 'rejected') as rejected,
        COUNT(*) as total
      FROM (
        SELECT status, is_eligible FROM thc_increase_questionnaire
        UNION ALL
        SELECT status, is_eligible FROM extend_tp_questionnaire
      ) combined_requests
    `;

    const result = await client.query(query);
    const stats = result.rows[0];

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', {
        pending: parseInt(stats.pending),
        approved: parseInt(stats.approved),
        rejected: parseInt(stats.rejected),
        total: parseInt(stats.total)
      }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get patient request history for admin lookup
 */
export const getAdminPatientRequestHistory: RequestHandler = catchAll(async (req, res) => {
  const { id: patientId } = req.params;
  const { limit = '20' } = req.query;
  const client = await db.connect();

  try {
    const query = `
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.total_score,
        tiq.max_score,
        tiq.status,
        tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        tiq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        tiq.approved_by as reviewed_by,
        tiq.review_notes,
        d.name as doctor_name
      FROM thc_increase_questionnaire tiq
      LEFT JOIN Dr d ON tiq.approved_by = d."accessID"
      WHERE tiq.patient_id = $1

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.total_score,
        etq.max_score,
        etq.status,
        etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        etq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        etq.approved_by as reviewed_by,
        etq.review_notes,
        d.name as doctor_name
      FROM extend_tp_questionnaire etq
      LEFT JOIN Dr d ON etq.approved_by = d."accessID"
      WHERE etq.patient_id = $1

      ORDER BY created_at DESC
      LIMIT $2
    `;

    const result = await client.query(query, [patientId, limit]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', { requests: result.rows }, true)
    );
  } finally {
    client.release();
  }
});
